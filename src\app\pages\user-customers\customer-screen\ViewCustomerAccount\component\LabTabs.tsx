import * as React from "react";
import Box from "@mui/material/Box";
import Tab from "@mui/material/Tab";
import TabContext from "@mui/lab/TabContext";
import TabList from "@mui/lab/TabList";
import TabPanel from "@mui/lab/TabPanel";
import { useColumnResponse } from "../../../../../../components/datatable/core/ColumnProvider";
import { useListCustomer } from "../../../core/ListCustomerProvider";
import dayjs from "dayjs";
import { _formatNumber, checkValidateAll, dateFormatTimeTH, numberWithCommas, setCurrentUsers } from "../../../../../../../libs/dataControl";
import ButtonAll from "../../../../../../components/ButtonAll";
import CustomizedDialogs from "../../../../../../components/MUI/CustomizedDialogs";
import DetailPlan from "./DetailPlan";
import CustomTable from "../../../../../../components/datatable/CustomTable";
import DetailBill from "./DetailBill";
import DetailHistoryPayment from "./DetailHistoryPayment";
import AddMessege from "./AddMessege";
import { TableCell, TableRow } from "@mui/material";
import ViewMessege from "./ViewMessege";
import DeleteMessege from "./DeleteMessege";
import EditMessege from "./EditMessege";
import { useDispatch, useSelector } from "react-redux";
import { _POST } from "../../../../../../service/mas";
import { useAuth } from "../../../../../modules/auth";
import { endEndLoadScreen, startLoadScreen } from "../../../../../../../redux/actions/loadingScreenAction";
import { SnackbarSet } from "../../../../../../components/MUI/SnackbarSet";
import { Massengmodal } from "../../../../../../components/MUI/Massengmodal";
import { UserActionsCellOther } from "../../../../../../components/datatable/UserActionsCellOther";
import { confirmModal } from "../../../../../../components/MUI/Comfirmmodal";
import { checkValidate } from "../../../../../../../libs/checkValidate/validate"
import TextFieldAmount from "../../../../../../components/MUI/TextFieldAmount";
import CustomTableSelectRepayment from "./CustomTableSelectRepayment";
import { getLovAll } from "../../../../../../service/lov";
import { findDataByColum, setValueMasView } from "../../../../../../../libs/setGetCallBackVaule";
import InterestRateAdjustment from "./InterestRateAdjustment";

interface LabTabs {
  customerGetWithId: (data: any, action: string) => void;
}

export default function LabTabs({ customerGetWithId }: LabTabs) {
  const menuFunc = useSelector((state: any) => state?.menuFunc);
  const screen_name = useSelector((state: any) => state?.labelName);
  const { currentUser } = useAuth();
  const { columnPlan, columHistoryRequestingmoney, columHistoryPayment, columAlertMessege, columUnApply } = useColumnResponse();
  const { itemDataCus, setItemDataCus, eventName, notes, startDate, endDate, emailNotification, checkNote,
    setEventName, setCheckNote, setNotes, setStartDate, setEndDate, setEmailNotification, optionUnApplyType, optionUnApplyStatus,
    interestRateAdjustment, setInterestRateAdjustment, dataInterestRateAdjustment, setDataInterestRateAdjustment, notesInterestRateAdjustment, setNotesInterestRateAdjustment
  } = useListCustomer();
  const [rePaymentType, setRePaymentType] = React.useState<any>([]);
  const [value, setValue] = React.useState("1");
  const [isOpenPlan, setIsOpenPlan] = React.useState(null);
  const [isOpenInterestRateAdjustment, setIsOpenInterestRateAdjustment] = React.useState<any>(null);
  const [isOpenBill, setIsOpenBill] = React.useState(null);
  const [isOpenDetail, setIsOpenDetail] = React.useState(null);
  const [isNotes, setIsNotes] = React.useState<any>({ add: false, edit: null, view: null, delete: null });
  const [account, setAccount] = React.useState<any>(null);
  const [accountNoteList, setAccountNoteList] = React.useState<any>([]);
  const [unApply, setUnApply] = React.useState<any>([]);
  const [dataSelect, setDataSelect] = React.useState<any>([]);
  const [valRepayment, setValRepayment] = React.useState<any>([]);
  // Check validates add notes
  const [isValidNote, setIsValidNote] = React.useState<any>(null);
  const dispatch = useDispatch();

  const handleChange = (event: React.SyntheticEvent, newValue: string) => {
    setValue(newValue);
  };

  const RePaymentType = async () => {
    const response = await getLovAll({ lov_type: 'RePaymentType' })
    setRePaymentType(response);
  }

  const customerModel = React.useMemo(() => {
    return itemDataCus[0]?.CustomerModel[0];
  }, [itemDataCus]);

  const planModel = React.useMemo(() => {
    const dataAccount = itemDataCus[0]?.AccountModel;
    setAccount(dataAccount[0]);
    const data = itemDataCus[0]?.PlanModel;
    const newdata: any = [];

    Array.isArray(data) &&
      data.forEach((el) => {
        el.ar_date_ = dateFormatTimeTH(el?.contract_date, "DD/MM/YYYY")
        el.prin_amount_ = el.prin_amount
          ? numberWithCommas(el.prin_amount.toFixed(2))
          : "0.00";
        el.prin_outs_ = el.prin_outs
          ? numberWithCommas(el.prin_outs.toFixed(2))
          : "0.00";
        el.interest_rate_ = el.interest_rate
          ? numberWithCommas(el.interest_rate.toFixed(2))
          : "0.00";
        el.interest_ = numberWithCommas(el.total_int_amount.toFixed(2));
        el.action = (
          <UserActionsCellOther viewName="ContractLoansView" menuOtherName="CONTRACTLOANS" meneWidth="200px" handleOnClick={(name) => {
            if (name == "ContractLoansView") {
              setIsOpenPlan(el);
            }
            if (name == "InterestRateAdjustment") {
              setIsOpenInterestRateAdjustment(el);
            }
          }} />
        );
        newdata.push(el);
      });
    return newdata;
  }, [itemDataCus]);

  const billingList = React.useMemo(() => {
    const data = itemDataCus[0]?.BillingModel;
    const newdata: any = [];
    Array.isArray(data) &&
      data.forEach((el) => {
        el.billing_date_ = dateFormatTimeTH(el?.billing_date, "DD/MM/YYYY")
        el.due_date_ = dateFormatTimeTH(el?.due_date, "DD/MM/YYYY")
        el.total_billing_ = el.total_billing
          ? numberWithCommas(el.total_billing.toFixed(2))
          : "0.00";
        el.action = (
          <ButtonAll
            btnName="ดูรายละเอียด"
            handleOnClick={() => setIsOpenBill(el)}
            size="btn-sm"
          />
        );
        newdata.push(el);
      });
    return newdata;
  }, [itemDataCus]);

  const repaymentList = React.useMemo(() => {
    if (value == "3") {
      const data = itemDataCus[0]?.RepaymentModel;
      const newdata: any = [];
      Array.isArray(data) &&
        data.forEach((el) => {
          el.repmt_date_ = el?.repmt_date ? dateFormatTimeTH(el?.repmt_date, "DD/MM/YYYY") : "";
          el.repmt_type_name = el?.repmt_type ? findDataByColum(rePaymentType, "id", el.repmt_type, "lov2") : "";
          el.repmt_amount_ = el?.repmt_amount ? numberWithCommas(el?.repmt_amount.toFixed(2)) : "0.00";
          el.posting_date_ = el?.posting_date ? dateFormatTimeTH(el?.posting_date, "DD/MM/YYYY") : "";
          el.detail = (
            <ButtonAll
              btnName="ดูรายละเอียด"
              size="btn-sm"
              handleOnClick={() => setIsOpenDetail(el)}
            />
          );
          newdata.push(el);
        });
      return newdata;
    }
    return [];
  }, [itemDataCus, value]);

  const getAccountNoteGet = async () => {
    const datasend = {
      "AccountNoteModel": {
        "id": null,
        "event_name": null,
        "acct_id": account ? account?.id : null,
        // "event_date_from" : null ,
        // "event_date_to" : null ,
        // "event_message" : null ,
        "event_status": null,
        "noti_to": null
      }
    }
    try {
      const response = await _POST(datasend, "AccountNote/Account_Note_Get");
      if (response.status == "success") {
        const { data: result } = response;
        const newData: any = []
        Array.isArray(result) && result.forEach((el) => {
          el.event_date_from_ = el.event_date_from ? dateFormatTimeTH(el.event_date_from, "DD/MM/YYYY") : ""
          el.event_date_to_ = el.event_date_to ? dateFormatTimeTH(el.event_date_to, "DD/MM/YYYY") : ""
          el.event_status_ = el.event_status ? (<div><label htmlFor="" className="text-green-600 font-bold">แจ้งเตือนแล้ว</label></div>) : (<div><label htmlFor="" className="text-red-600 font-bold">ยังไม่แจ้งเตือน</label></div>)
          el.action = (<UserActionsCellOther viewName="ViewNote" menuOtherName="NOTE" handleOnClick={(name) => {
            if (name == "ViewNote") {
              setIsNotes({ ...isNotes, ...{ add: false, edit: null, view: el, delete: null } })
            }
            if (name == "EditNote") {
              setIsNotes({ ...isNotes, ...{ add: false, edit: el, view: null, delete: null } })
            }
            if (name == "DeleteNote") {
              setIsNotes({ ...isNotes, ...{ add: false, edit: null, view: null, delete: el } })
            }
          }} />)
          newData.push(el);
        });
        setAccountNoteList(newData)
      } else {
        SnackbarSet(response.error_message, "error")
      }
    } catch (error) {
      console.log(error);
      SnackbarSet(String(error), "error")
    }
  }

  const handleAddNotes = async () => {
    const isCheckValid = checkValidate({ eventName, startDate, endDate, notes, emailNotification }, ['eventName', 'startDate', 'endDate'])
    const isValid = checkValidateAll(isCheckValid)
    if (Object.keys(isValid).length > 0) {
      setIsValidNote(isValid)
      SnackbarSet("กรุณากรอกข้อมูลให้ครบถ้วน !", 'error')
      return
    }
    setIsValidNote(isValid)
    confirmModal.createModal("ต้องการบันทึกข้อมูลใช่หรือไม่", "success", async () => {
      dispatch(startLoadScreen())
      const datasend = {
        "AccountNoteModel": {
          "event_name": eventName ? eventName?.id : null,
          "acct_id": account ? account?.id : null,
          "event_date_from": startDate ? dayjs(startDate).format("YYYY-MM-DD") : null,
          "event_date_to": endDate ? dayjs(endDate).format("YYYY-MM-DD") : null,
          "event_message": notes ? notes : null,
          "event_status": checkNote,
          "noti_to": emailNotification ? emailNotification : "",
        },
        CurrentAccessModel: await setCurrentUsers(currentUser, screen_name, "Account_Note_Add")
      }
      try {
        const response = await _POST(datasend, "AccountNote/Account_Note_Add");
        if (response.status == "success") {
          dispatch(endEndLoadScreen())
          Massengmodal.createModal("บันทึกข้อมูลสำเร็จ", "success", () => {
            handleCloseNote()
            getAccountNoteGet()
            setIsValidNote(null)
          })
        } else {
          dispatch(endEndLoadScreen())
          SnackbarSet(response.error_message, "error")
        }
      } catch (error) {
        console.log(error);
        dispatch(endEndLoadScreen())
        SnackbarSet(String(error), "error")
      }
    })
  }
  const handleEditNotes = async () => {
    const isCheckValid = checkValidate({ eventName, startDate, endDate, notes, emailNotification }, ['eventName', 'startDate', 'endDate'])
    const isValid = checkValidateAll(isCheckValid)
    if (Object.keys(isValid).length > 0) {
      setIsValidNote(isValid)
      SnackbarSet("กรุณากรอกข้อมูลให้ครบถ้วน !", 'error')
      return
    }
    setIsValidNote(isValid)
    confirmModal.createModal("ต้องการแก้ไขข้อมูลใช่หรือไม่", "success", async () => {
      dispatch(startLoadScreen())
      const datasend = {
        "AccountNoteModel": {
          "id": isNotes.edit?.id,
          "event_name": eventName ? eventName?.id : null,
          "acct_id": account ? account?.id : null,
          "event_date_from": startDate ? dayjs(startDate).format("YYYY-MM-DD") : null,
          "event_date_to": endDate ? dayjs(endDate).format("YYYY-MM-DD") : null,
          "event_message": notes ? notes : null,
          "event_status": checkNote,
          "noti_to": emailNotification ? emailNotification : "",
        },
        CurrentAccessModel: await setCurrentUsers(currentUser, screen_name, "Account_Note_Edit"),
      }
      try {
        const response = await _POST(datasend, "AccountNote/Account_Note_Edit");
        if (response.status == "success") {
          dispatch(endEndLoadScreen())
          Massengmodal.createModal("แก้ไขข้อมูลสำเร็จ", "success", () => {
            handleCloseNote()
            getAccountNoteGet()
            setIsValidNote(null)
          })
        } else {
          dispatch(endEndLoadScreen())
          SnackbarSet(response.error_message, "error")
        }
      } catch (error) {
        console.log(error);
        dispatch(endEndLoadScreen())
        SnackbarSet(String(error), "error")
      }
    })
  }
  const handleDeleteNotes = async () => {
    confirmModal.createModal("ต้องการลบใช่หรือไม่", "success", async () => {
      dispatch(startLoadScreen())
      const datasend = {
        "AccountNoteModel": {
          "id": isNotes.delete?.id,
        },
        "CurrentAccessModel": await setCurrentUsers(currentUser, screen_name, "Account_Note_Delete"),
      }
      try {
        const response = await _POST(datasend, "AccountNote/Account_Note_Delete");
        if (response.status == "success") {
          dispatch(endEndLoadScreen())
          Massengmodal.createModal("ลบข้อมูลสำเร็จ", "success", () => {
            handleCloseNote()
            getAccountNoteGet()
          })
        } else {
          dispatch(endEndLoadScreen())
          SnackbarSet(response.error_message, "error")
        }
      } catch (error) {
        console.log(error);
        dispatch(endEndLoadScreen())
        SnackbarSet(String(error), "error")
      }
    })
  }

  const handleCloseNote = () => {
    setIsNotes({ add: false, edit: null, view: null, delete: null });
    setEventName(null);
    setCheckNote(false);
    setNotes(null);
    setStartDate(null);
    setEndDate(null);
    setEmailNotification(null);
    setIsValidNote(null)
  };

  const Un_Apply_Get = async () => {
    const newData: any = [];
    const datasend = {
      "UnApplyModel": {
        "id": null,
        "acct_id": account?.id,
        "no_book_id": null,
        "un_apply_no": null,
        "un_apply_type": null,
        "un_apply_status": null
      }
    }
    const response = await _POST(datasend, "UnApply/Un_Apply_Get");
    if (response.status == "success") {
      const { data: result } = response;
      Array.isArray(result) && result.forEach((el, index) => {
        el.select = el.un_apply_balance == 0 ? "" : "select"
        el.acc_no = account?.acct_no
        // el.un_apply_date_ = dayjs(endDate).format("YYYY-MM-DD");
        el.un_apply_date_ = el?.un_apply_date ? dateFormatTimeTH(el?.un_apply_date, "DD/MM/YYYY") : "";
        el.un_apply_amount_ = el?.un_apply_amount ? numberWithCommas(el?.un_apply_amount.toFixed(2)) : "0.00";
        el.un_apply_balance_ = el?.un_apply_balance ? numberWithCommas(el?.un_apply_balance.toFixed(2)) : "0.00";
        el.un_apply_type_ = setValueMasView(optionUnApplyType, el.un_apply_type, "id", "lov1")
        el.un_apply_status_ = setValueMasView(optionUnApplyStatus, el.un_apply_status, "id", "lov_code")
        el.key_value = <TextFieldAmount value={el.un_apply_balance == 0 ? el.un_apply_balance : "0"} limitNumber={el?.un_apply_balance} onchange={(val: string) => onChangeRepayment(val, index, el)} disabled={el.un_apply_balance == 0} />
        if (["unapply_status_pending", "unapply_status_repayment"].includes(el.un_apply_status)) {
          newData.push(el);
        }
      })
      setUnApply([]);
      setUnApply(newData);
    } else {
      SnackbarSet(response.error_message, "error")
    }
  }
  const onChangeRepayment = (val: string, index: number, data: any) => {
    // Update the repayment data state
    setValRepayment((prev: any[]) => {
      const updated = [...prev]; // Clone the existing array
      if (updated[index]) {
        updated[index] = { ...updated[index], un_apply_balance: val, id: data.id, un_apply_type: data.un_apply_type, un_apply_date: data.un_apply_date }; // Update the specific object
      } else {
        updated[index] = { un_apply_balance: val, id: data.id, un_apply_type: data.un_apply_type, un_apply_date: data.un_apply_date }; // Initialize if the object doesn't exist
      }
      console.log(updated); // Log the updated array
      return updated;
    });
  };


  console.log(dataSelect);


  const handleRepayment = async () => {
    if (dataSelect.length === 0) {
      Massengmodal.createModal("กรุณาเลือกรายการที่จะชำระ", "error", () => {
      })
      return
    }

    const checkId: any = dataSelect.map((data: any) => data.id);
    const unApply = valRepayment.filter((val: any) => val != undefined && checkId.includes(val.id));
    const isCheck = unApply.filter((val: any) => val.un_apply_balance == "");

    if (dataSelect.length != unApply.length) {
      Massengmodal.createModal("กรุณากรอกยอดที่จะชำระ ที่เลือกรายการ", "error", () => {
      })
      return
    }
    if (isCheck.length != 0) {
      Massengmodal.createModal("กรุณากรอกยอดที่จะชำระ ที่เลือกรายการ", "error", () => {
      })
      return
    }
    dispatch(startLoadScreen());
    const datasend = {
      "RepaymentModel": {
        "acct_id": account?.id,
        // "repmt_date": dayjs().format("YYYY-MM-DD")
        "repmt_date": "2025-08-31"
      },
      "UnApplyModelList": [
        ...unApply
      ],
      CurrentAccessModel: await setCurrentUsers(currentUser, screen_name, "Un_Apply_Repayment"),
    }
    const response = await _POST(datasend, "UnApply/Un_Apply_Repayment");
    if (response.status == "success") {
      dispatch(endEndLoadScreen());
      setUnApply([])
      setDataSelect([]);
      setValRepayment([]);
      Massengmodal.createModal("ชำระเงินสำเร็จ", "success", async () => {
        await Un_Apply_Get();
        customerGetWithId({ id: customerModel?.id }, "Account")
      })
    } else {
      dispatch(endEndLoadScreen());
      SnackbarSet(response.error_message, "error")
    }

  };

  const handleInterestRateAdjustment = async () => {
    if (!interestRateAdjustment) {
      Massengmodal.createModal("กรุณากรอกข้อมูล", "error", () => {
      })
      return
    }
    if (!dataInterestRateAdjustment) {
      Massengmodal.createModal("กรุณาเลือกวันที่มีผลใช้งาน", "error", () => {
      })
      return
    }
    
    confirmModal.createModal("ต้องการเปลี่นแปลงใช่ไหม", "info", async () => {
      dispatch(startLoadScreen());
      const datasend = {
        "PlanModel": {
          "id": isOpenInterestRateAdjustment?.id
        },
        "PlanIntRateLogModel": {
          "plan_id": isOpenInterestRateAdjustment?.id,
          "before_interest_rate": isOpenInterestRateAdjustment?.interest_rate,
          "after_interest_rate": interestRateAdjustment,
          "ef_date": dayjs(dataInterestRateAdjustment).format("YYYY-MM-DD"),
          "int_log_note": notesInterestRateAdjustment
        },
        "CurrentAccessModel": await setCurrentUsers(currentUser, screen_name, "Interest_Rate_Adjustment_Add")
      }

      try {
        const response = await _POST(datasend, "Plan/Create_Change_Int_Rate");
        if (response.status == "success") {
          dispatch(endEndLoadScreen())
          Massengmodal.createModal("เปลี่นแปลงสำเร็จ", "success", () => {
            setIsOpenInterestRateAdjustment(null)
            setInterestRateAdjustment(null)
            setDataInterestRateAdjustment(null)
            setNotesInterestRateAdjustment(null)
          })
        } else {
          dispatch(endEndLoadScreen())
          SnackbarSet(response.error_message, "error")
        }
      } catch (error) {
        console.log(error);
        dispatch(endEndLoadScreen())
        SnackbarSet(String(error), "error")
      }
    })
  }


  React.useEffect(() => {
    RePaymentType();
  }, []);

  React.useEffect(() => {
    getAccountNoteGet()
    Un_Apply_Get();
  }, [account]);

  React.useEffect(() => {
    if (value == '5' && optionUnApplyStatus.length > 0 && optionUnApplyType.length > 0) {
      Un_Apply_Get();
    }
  }, [value, optionUnApplyStatus, optionUnApplyType]);

  return (
    <Box sx={{ width: "100%", typography: "body1" }}>
      <TabContext value={value}>
        <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
          <TabList onChange={handleChange} aria-label="lab API tabs example">
            <Tab label="ข้อมูลสินเชื่อรายสัญญา" value="1" />
            <Tab label="ประวัติการเรียกเก็บเงิน" value="2" />
            <Tab label="ประวัติการชำระ" value="3" />
            <Tab label="บันทึกหมายเหตุ" value="4" />
            <Tab label="ชำระ" value="5" />
          </TabList>
        </Box>
        <TabPanel value="1">
          <CustomTable
            titlename="ข้อมูลสินเชื่อรายสัญญา"
            rows={planModel}
            colum={columnPlan}
            noDataname="ไม่พบรายการข้อมูลสินเชื่อรายสัญญา"
            componentColSum={<ColumnSum data={planModel} />}
          />
        </TabPanel>
        <TabPanel value="2">
          <CustomTable
            titlename="ประวัติการเรียกเก็บเงิน"
            rows={billingList}
            colum={columHistoryRequestingmoney}
            noDataname="ไม่พบรายการประวัติการเรียกเก็บเงิน"
          />
        </TabPanel>
        <TabPanel value="3">
          <CustomTable
            titlename="ประวัติการชำระ"
            rows={repaymentList}
            colum={columHistoryPayment}
            noDataname="ไม่พบรายการประวัติการชำระ"
          />
        </TabPanel>
        <TabPanel value="4">
          <CustomTable
            titlename="ตารางบันทึกหมายเหตุ"
            rows={accountNoteList}
            colum={columAlertMessege}
            noDataname="ไม่พบรายการแจ้งเตือน"
            btnAdd={<ButtonAll btnName={menuFunc?.AddNote} size="btn-sm" handleOnClick={() => setIsNotes({ ...isNotes, add: true })} className={menuFunc?.AddNote ? "btn btn-light-primary" : "d-none"} />}
          />
        </TabPanel>
        <TabPanel value="5">
          <label htmlFor="" className="text-2xl">ชำระสินเชื่อ</label>
          <CustomTableSelectRepayment
            titlename="รายการชำระที่ยังไม่อนุมัติ"
            rows={unApply}
            colum={columUnApply}
            noDataname="ไม่พบรายการชำระที่ยังไม่อนุมัติ"
            isCheck={dataSelect}
            setDataSelect={setDataSelect}
          // btnAdd={<ButtonAll btnName={menuFunc?.AddNote} size="btn-sm" handleOnClick={() => setIsNotes({ ...isNotes, add: true })} className={menuFunc?.AddNote ? "btn btn-light-primary" : "d-none"} />}
          />
          <div className="flex justify-end space-x-2">
            <ButtonAll
              btnName="บันทึก"
              size="btn-sm"
              handleOnClick={handleRepayment}
            />
            <ButtonAll
              btnName="ปิด"
              size="btn-sm"
              handleOnClick={() => setItemDataCus(undefined)}
              className="btn btn-light-danger"
            />
          </div>
        </TabPanel>
      </TabContext>

      <CustomizedDialogs
        titleName="ดูรายละเอียดสินเชื่อ"
        isOpen={isOpenPlan != null}
        handleClose={() => setIsOpenPlan(null)}
        maxWidth="lg"
        elementBody={<DetailPlan detailPlan={isOpenPlan} />}
      />
      <CustomizedDialogs
        titleName="ปรับเปลี่ยนอัตตราดอกเบี้ย"
        isOpen={isOpenInterestRateAdjustment != null}
        handleClose={() => setIsOpenInterestRateAdjustment(null)}
        handleSave={handleInterestRateAdjustment}
        maxWidth="sm"
        elementBody={<InterestRateAdjustment detailPlan={isOpenInterestRateAdjustment} />}
      />
      <CustomizedDialogs
        titleName="ดูรายละเอียดบิล"
        isOpen={isOpenBill != null}
        handleClose={() => {
          setIsOpenBill(null);
        }}
        maxWidth="lg"
        elementBody={<DetailBill dataBill={isOpenBill} />}
      />
      <CustomizedDialogs
        titleName="ดูรายละเอียดการชำระ"
        isOpen={isOpenDetail != null}
        handleClose={() => { setIsOpenDetail(null) }}
        maxWidth="lg"
        elementBody={<DetailHistoryPayment dataRepayment={isOpenDetail} />}
      />
      <CustomizedDialogs
        titleName="เพิ่ม/บันทึกหมายเหตุ"
        isOpen={isNotes?.add}
        handleSave={handleAddNotes}
        handleClose={handleCloseNote}
        maxWidth="md"
        elementBody={<AddMessege isValidate={isValidNote} />}
      />
      <CustomizedDialogs
        titleName="ดูข้อมูล/บันทึกหมายเหตุ"
        isOpen={isNotes?.view != null}
        handleClose={handleCloseNote}
        maxWidth="md"
        elementBody={<ViewMessege dataView={isNotes?.view} />}
      />
      <CustomizedDialogs
        titleName="ลบ/บันทึกหมายเหตุ"
        isOpen={isNotes?.delete != null}
        handleDelete={handleDeleteNotes}
        handleClose={handleCloseNote}
        maxWidth="md"
        elementBody={<DeleteMessege dataView={isNotes?.delete} />}
      />
      <CustomizedDialogs
        titleName="แก้ไข/บันทึกหมายเหตุ"
        isOpen={isNotes?.edit != null}
        handleEdit={handleEditNotes}
        handleClose={handleCloseNote}
        maxWidth="md"
        elementBody={<EditMessege dataView={isNotes?.edit} isValidate={isValidNote} />}
      />
    </Box>
  );
}

interface ColumnSum {
  data: any;
}

function ColumnSum({ data }: ColumnSum) {
  const { columnPlan } = useColumnResponse();

  const totalSum_prin_amount = data.reduce(
    (sum: number, row: any) => sum + parseFloat(row[`prin_amount`]),
    0
  );
  const totalSum_prin_outs = data.reduce(
    (sum: number, row: any) => sum + parseFloat(row[`prin_outs`]),
    0
  );
  const totalSum_total_int_amount = data.reduce(
    (sum: number, row: any) => sum + parseFloat(row[`total_int_amount`]),
    0
  );

  return (
    <TableRow>
      <TableCell
        colSpan={columnPlan.length - 4}
        align="center"
        className="border-r border-r-gray-500/10"
      >
        <label className="fs-6 text-gray-500">{"รวม"}</label>
      </TableCell>
      <TableCell align="center" className="border-r border-r-gray-500/10">
        <label className="fs-6 text-gray-500">
          {totalSum_prin_amount
            ? numberWithCommas(totalSum_prin_amount.toFixed(2))
            : ""}
        </label>
      </TableCell>
      <TableCell align="center" className="border-r border-r-gray-500/10">
        <label className="fs-6 text-gray-500">
          {totalSum_prin_outs
            ? numberWithCommas(totalSum_prin_outs.toFixed(2))
            : ""}
        </label>
      </TableCell>
      <TableCell align="center" className="border-r border-r-gray-500/10">
        <label className="fs-6 text-gray-500">
          {totalSum_total_int_amount
            ? numberWithCommas(totalSum_total_int_amount.toFixed(2))
            : ""}
        </label>
      </TableCell>
      <TableCell
        align="center"
        className="border-r border-r-gray-500/10"
      ></TableCell>
    </TableRow>
  );
}


