import React from 'react'
import DatePickerBasic from '../../../components/MUI/DatePickerBasic'
import dayjs from 'dayjs';
import { _POST } from '../../../service/mas';
import { getLovAll } from '../../../service/lov';

export type Holiday = {
  id: string;
  holiday_date: string;
  holiday_name: string;
  create_by: string;
  update_by: string | null;
}

export default function index() {
  const [dayPayment, setDayPayment] = React.useState<dayjs.Dayjs | undefined>();
  const [dataHoliday, setDataHoliday] = React.useState<Holiday[]>([]);
  const [minDate, setMinDate] = React.useState<dayjs.Dayjs | null>(null);

  const Holiday_Get = async () => {
    //API ในการด��งข้อมูลวันหยุด
    const dataSend = {
      "id": null,
      "holiday_date": null,
      "holiday_name": null
    }
    try {
      const reponse = await _POST(dataSend, "Holiday/Holiday_Get");
      if (reponse && reponse.status == "success") {
        setDataHoliday(reponse.data);
      }
    } catch (e) {
      console.log(e);
    }
  }

  // Disable weekends and holidays function
  const disableWeekendsAndHolidays = (date: Date): boolean => {
    // Check if the date is a weekend
    const day = dayjs(date).format("dddd");
    if (day === "เสาร์" || day === "อาทิตย์") {
      return true; // Disable the date if it's Saturday or Sunday
    }

    // Check if the date matches any holiday in dataHoliday
    const formattedDate = dayjs(date).format("YYYY-MM-DD");
    const isHoliday = dataHoliday.some(
      (holiday) =>{
        return dayjs(holiday.holiday_date).format("YYYY-MM-DD") === formattedDate
      }
    );

    return isHoliday; // Disable the date if it's a holiday
  };

  function adjustToNextValidDay(today: dayjs.Dayjs): dayjs.Dayjs {
    // Start with the current day
    const currentDay = today.startOf("day");

    // Extract holiday dates from dataHoliday
    const holidays = dataHoliday.map((holiday) =>
      dayjs(holiday.holiday_date).format("YYYY-MM-DD")
    );

    // Helper function to check if a date is a holiday or weekend
    const isHolidayOrWeekend = (date: dayjs.Dayjs): boolean => {
      const day = date.day(); // 0 = Sunday, 6 = Saturday
      const formattedDate = date.format("YYYY-MM-DD");

      // Check if it's a weekend
      if (day === 0 || day === 6) return true;

      // Check if it's a holiday
      return holidays.includes(formattedDate);
    };

    // If the current day is a valid business day, count it as 2 business days
    // If it's a holiday or weekend, we need to find 2 business days
    // (effectively adding an additional day)

    // Count the current day as 2 business days
    let businessDaysToAdd = 2;

    // Skip over any holidays or weekends
    let nextDay = currentDay;
    while (businessDaysToAdd > 0) {
      nextDay = nextDay.add(1, "day").startOf("day");

      // Only count valid business days
      if (!isHolidayOrWeekend(nextDay)) {
        businessDaysToAdd--;
      }
    }

    return nextDay;
  }


  React.useEffect(() => {
    const setDate = async () => {
      const disbursedDate: any = await getLovAll({ id: "advance_disburse_day", type_code: "Constant" });
      // const day = dayjs().add(Number(disbursedDate?.[0]?.lov1), "day").startOf("day");
      // disbursedDate?.[0]?.lov1 == 2
      console.log(disbursedDate?.[0]?.lov1, 'disbursedDatad');

      const day = dayjs();
      console.log(day, 'daydayday');

      const toDay = adjustToNextValidDay(day);
      setDayPayment(toDay);
      setMinDate(dayjs());
    }
    setDate();
  }, [dataHoliday]);

  React.useEffect(() => {
    Holiday_Get();
  }, []);
  return (
    <div>
      <DatePickerBasic
        labelname="วันที่จ่าย"
        valueStart={dayPayment}
        onchangeStart={setDayPayment}
        disableHighlightToday
        disableWeekends={disableWeekendsAndHolidays}
        minDate={minDate}
      />
    </div>
  )
}
