import dayjs from "dayjs";
import { SetStateAction, Dispatch } from "react";

export type Adds = undefined | null | boolean;
export type Texts = undefined | null | string;

export type useListWaiveContextProps = {
  actionName: string;
  setActionName: Dispatch<SetStateAction<string>>;
  itemView?: any;
  setItemView: Dispatch<SetStateAction<any>>;
  customerNunber?: Texts;
  fullName?: Texts;
  citizenId?: Texts;
  sex?: any;
  email?: Texts;
  phone?: Texts;
  setCustomerNunber: Dispatch<SetStateAction<Texts>>;
  setFullName: Dispatch<SetStateAction<Texts>>;
  setCitizenId: Dispatch<SetStateAction<Texts>>;
  setSex: Dispatch<SetStateAction<any>>;
  setEmail: Dispatch<SetStateAction<Texts>>;
  setPhone: Dispatch<SetStateAction<Texts>>;


  // MASTER Address
  optionProvinces: any;
  setOptionProvinces: Dispatch<SetStateAction<any>>;
  optionDistricts: any;
  setOptionDistricts: Dispatch<SetStateAction<any>>;
  optionSubDistricts: any;
  setOptionSubDistricts: Dispatch<SetStateAction<any>>;
  // LOV
  optionWaiveType: any;
  setOptionWaiveType: Dispatch<SetStateAction<any>>;
  // waive charge
  waiveDate: dayjs.Dayjs | null;
  setWaiveDate: Dispatch<SetStateAction<dayjs.Dayjs | null>>;
  waiveNote: string;
  setWaiveNote: Dispatch<SetStateAction<string>>;
  valueWaive: any;
  setValueWaive: Dispatch<SetStateAction<any>>;
};

export const initialListView: useListWaiveContextProps = {
  actionName: "",
  setActionName: () => { },
  setItemView: () => { },
  setFullName: () => { },
  setCitizenId: () => { },
  setSex: () => { },
  setEmail: () => { },
  setPhone: () => { },
  setCustomerNunber: () => { },
  // MASTER Address
  optionProvinces: [],
  setOptionProvinces: () => { },
  optionDistricts: [],
  setOptionDistricts: () => { },
  optionSubDistricts: [],
  setOptionSubDistricts: () => { },
  // LOV
  optionWaiveType: [],
  setOptionWaiveType: () => { },
  // waive charge
  waiveDate: null,
  setWaiveDate: () => { },
  waiveNote: "",
  setWaiveNote: () => { },
  valueWaive: null,
  setValueWaive: () => { },
};
