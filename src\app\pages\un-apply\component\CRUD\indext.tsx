import React from 'react'
import CustomTable from '../../../../../components/datatable/CustomTable'
import { unapprovedPayments } from '../../../../../../libs/columeCustomTable';
import { _POST } from '../../../../../service/mas';
import { useDispatch, useSelector } from 'react-redux';
import { useAuth } from '../../../../modules/auth';
import { endEndLoadScreen, startLoadScreen } from '../../../../../../redux/actions/loadingScreenAction';
import { dateFormatTimeTH, numberWithCommas, setCurrentUsers } from '../../../../../../libs/dataControl';
import { UserActionsCellOther } from '../../../../../components/datatable/UserActionsCellOther';
import { SnackbarSet } from '../../../../../components/MUI/SnackbarSet';
import { confirmModal } from '../../../../../components/MUI/Comfirmmodal';
import { Massengmodal } from '../../../../../components/MUI/Massengmodal';
import CustomizedDialogs from '../../../../../components/MUI/CustomizedDialogs';
import { useListUnApply } from '../../core/ListUnApplyProvider';
import ViewUnapprovedPayments from './ViewUnapprovedPayments';
import { setValueMasView } from '../../../../../../libs/setGetCallBackVaule';

interface UnApplyCRUD {
    accountModel?: any
    isTab?: string
}

export default function UnApplyCRUD({ accountModel, isTab }: UnApplyCRUD) {
    const screen_name = useSelector((state: any) => state?.labelName);
    const { currentUser } = useAuth();
    const dispatch = useDispatch();
    const { itemView, optionUnApplyType, optionUnApplyStatus } = useListUnApply();
    const [isOpenUnApply, setIsOpenUnApply] = React.useState<any>(null);
    const [unApplyList, setUnApplyList] = React.useState([]);
    const [actionName, setActionName] = React.useState<string>("UnapplyView");

    // Un apply
    const handleGetUnApply = async () => {
        const datasend = {
            "UnApplyModel": {
                "id": null,
                "acct_id": accountModel?.id,
                "no_book_id": null,
                "un_apply_no": null,
                "un_apply_type": null,
                "un_apply_status": null
            }
        }
        try {
            const response = await _POST(datasend, "UnApply/Un_Apply_Get");
            if (response && response.status == "success") {
                dispatch(endEndLoadScreen())
                const { data: result } = response
                const newData: any = []
                Array.isArray(result) && result.forEach((el) => {
                    el.account_no = accountModel?.acct_no
                    el.un_apply_date_ = dateFormatTimeTH(el.un_apply_date, 'DD/MM/YYYY')
                    el.un_apply_amount_ = el.un_apply_amount ? numberWithCommas(el.un_apply_amount.toFixed(2)) : "0.00"
                    el.un_apply_balance_ = el.un_apply_balance ? numberWithCommas(el.un_apply_balance.toFixed(2)) : "0.00"
                    el.un_apply_type_ = setValueMasView(optionUnApplyType, el.un_apply_type, "id", "lov1")
                    el.un_apply_status_ = setValueMasView(optionUnApplyStatus, el.un_apply_status, "id", "lov_code")
                    el.action = (<UserActionsCellOther
                        viewName=""
                        menuOtherName="UNAPPLY"
                        // hiddenStatusRow={["unapply_status_reject", "unapply_status_close"].includes(el.un_apply_status) ? "View" : ""}
                        funcMenuShow={el.un_apply_type == "unapply_type_nobook" && el.un_apply_status == "unapply_status_pending" ? ["UnapplyRefund"] : []}
                        handleOnClick={(name) => {
                            setActionName(name);
                            setIsOpenUnApply(el);
                        }} />)

                    if (isTab == "Current") {
                        if (!["unapply_status_reject", "unapply_status_close"].includes(el.un_apply_status)) {
                            newData.push(el)
                        }
                    }
                    if (isTab == "History") {
                        if (["unapply_status_reject", "unapply_status_close"].includes(el.un_apply_status)) {
                            newData.push(el)
                        }
                    }
                })
                setUnApplyList(newData)
            } else if (response && response.status == "error") {
                dispatch(endEndLoadScreen())
                SnackbarSet(response.error_message, "error")
            } else {
                dispatch(endEndLoadScreen())
                SnackbarSet(`Call API ${response}`, "error")
            }
        } catch (error) {
            dispatch(endEndLoadScreen())
            console.log(error);
            SnackbarSet(`${error}`, "error")
        }
    }

    const handleCancelUnApply = async () => {
        confirmModal.createModal("ต้องการยกเลิกใช่หรือไม่", "info", async () => {
            dispatch(startLoadScreen())
            const datasendUnApply = {
                "UnApplyModel": {
                    "id": isOpenUnApply?.id,
                    "acct_id": null,
                    "no_book_id": null,
                    "un_apply_no": null,
                    "un_apply_date": null,
                    "un_apply_amount": null,
                    "un_apply_balance": null,
                    "un_apply_type": null,
                    "un_apply_status": "unapply_status_reject"
                },
                "CurrentAccessModel": await setCurrentUsers(currentUser, screen_name, "Un_Apply_Edit")
            }
            try {
                const response = await _POST(datasendUnApply, "UnApply/Un_Apply_Edit");
                if (response && response.status == "success") {
                    // dispatch(endEndLoadScreen())
                    handleDraftNobook()
                } else if (response && response.status == "error") {
                    dispatch(endEndLoadScreen())
                    SnackbarSet(response.error_message, "error")
                } else {
                    dispatch(endEndLoadScreen())
                    SnackbarSet(`Call API ${response}`, "error")
                }
            } catch (error) {
                // dispatch(endEndLoadScreen())
                console.log(error);
                SnackbarSet(`${error}`, "error")
            }
        })

        const handleDraftNobook = async () => {
            const datasend = {
                "RepaymentNoBookModel": {
                    "id": isOpenUnApply.no_book_id,
                    "no_book_status": "Draft"
                },
                "CurrentAccessModel": await setCurrentUsers(currentUser, screen_name, "Repayment_No_Book_Edit")
            }
            try {
                const response = await _POST(datasend, "RepaymentNoBook/Repayment_No_Book_Edit");
                if (response && response.status == "success") {
                    dispatch(endEndLoadScreen())
                    Massengmodal.createModal("ยกเลิกเรียบร้อย", "success", () => {
                        handleCloseUnApply()
                        handleGetUnApply()
                    })
                } else if (response && response.status == "error") {
                    dispatch(endEndLoadScreen())
                    SnackbarSet(response.error_message, "error")
                } else {
                    dispatch(endEndLoadScreen())
                    SnackbarSet(`Call API ${response}`, "error")
                }
            } catch (error) {
                dispatch(endEndLoadScreen())
                console.log(error);
                SnackbarSet(`${error}`, "error")
            }

        }
    }

    const handleCloseUnApply = () => {
        setIsOpenUnApply(null);
    }

    const customerModel = React.useMemo(() => {
        return itemView[0]?.CustomerModel[0];
    }, [itemView]);


    React.useEffect(() => {
        if (accountModel && optionUnApplyType.length > 0 && optionUnApplyStatus.length > 0) {
            handleGetUnApply()
        }
    }, [accountModel, optionUnApplyType, optionUnApplyStatus])

    return (
        <div>
            <CustomTable
                titlename="ข้อมูลการชำระที่ยังไม่อนุมัติ"
                rows={unApplyList}
                colum={unapprovedPayments}
                noDataname="ไม่พบข้อมูลการชำระที่ยังไม่อนุมัติ"
            />

            {/* ........................................................................................................ */}
            <CustomizedDialogs
                isOpen={isOpenUnApply != null}
                titleName={`${titleName[`${actionName}`]} / Un Apply List`}
                handleClose={handleCloseUnApply}
                handleCancel={actionName == "UnapplyCencel" ? handleCancelUnApply : undefined}
                maxWidth="lg"
                elementBody={
                    <ViewUnapprovedPayments accountModel={accountModel} customerModel={customerModel} dataView={isOpenUnApply} />
                }
            />
        </div>
    )
}

const titleName: Record<string, string> = {
    "UnapplyView": "ดูข้อมูล",
    "UnapplyCencel": "ยกเลิก",
    "UnapplyRefund": "ทำคืนเงิน",
};
