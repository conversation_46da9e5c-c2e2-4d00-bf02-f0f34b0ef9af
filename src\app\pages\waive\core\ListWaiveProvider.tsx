import { createContext, FC, useContext, useState } from "react";
import { initialListView, Texts, useListWaiveContextProps } from "./models";
import { WithChildren } from "../../../../_metronic/helpers";
import dayjs from "dayjs";


const ListManualWaiveContext =
  createContext<useListWaiveContextProps>(initialListView);
const ListWaiveProvider: FC<WithChildren> = ({ children }) => {
 const [actionName, setActionName] = useState<string>(initialListView.actionName);
  const [itemView, setItemView] = useState<Array<any[]>>(
    initialListView.itemView
  );

  const [customerNunber, setCustomerNunber] = useState<Texts>(
    initialListView.customerNunber
  );
  const [fullName, setFullName] = useState<Texts>(
    initialListView.fullName
  );
  const [citizenId, setCitizenId] = useState<Texts>(
    initialListView.citizenId
  );
  const [sex, setSex] = useState<any>(
    initialListView.sex
  );
  const [email, setEmail] = useState<Texts>(
    initialListView.email
  );
  const [phone, setPhone] = useState<Texts>(
    initialListView.phone
  );

   // MASTER Address
   const [optionProvinces, setOptionProvinces] = useState<Array<any[]>>(
    initialListView.optionProvinces
  );
  const [optionDistricts, setOptionDistricts] = useState<Array<any[]>>(
    initialListView.optionDistricts
  );
  const [optionSubDistricts, setOptionSubDistricts] = useState<Array<any[]>>(
    initialListView.optionSubDistricts
  );
  // LOV
  const [optionWaiveType, setOptionWaiveType] = useState<Array<any[]>>(
    initialListView.optionWaiveType
  );

  // waive charge
  const [waiveDate, setWaiveDate] = useState<dayjs.Dayjs | null>(initialListView.waiveDate);
  const [waiveNote, setWaiveNote] = useState<string>(initialListView.waiveNote);
  const [valueWaive, setValueWaive] = useState<any>(initialListView.valueWaive);

  return (
    <ListManualWaiveContext.Provider
      value={{
        actionName,
        setActionName,
        itemView,
        setItemView,

        customerNunber,
        fullName,
        citizenId,
        sex,
        email,
        phone,
        setCustomerNunber,
        setFullName,
        setCitizenId,
        setSex,
        setEmail,
        setPhone,
        // MASTER Address
        optionProvinces,
        setOptionProvinces,
        optionDistricts,
        setOptionDistricts,
        optionSubDistricts,
        setOptionSubDistricts,
        // LOV
        optionWaiveType,
        setOptionWaiveType,
        // waive charge
        waiveDate,
        setWaiveDate,
        waiveNote,
        setWaiveNote,
        valueWaive,
        setValueWaive,
      }}
    >
      {children}
    </ListManualWaiveContext.Provider>
  );
};

const useListWaive = () => useContext(ListManualWaiveContext);

export { ListWaiveProvider, useListWaive };
