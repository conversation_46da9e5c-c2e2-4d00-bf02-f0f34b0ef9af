import axios from "axios";
import { getValidToken } from "../token";

export async function plg_uploadFileRename(
  element: any,
  path: string,
  rename: string
) {
  const data = new FormData();
  const getFile = element;

  data.append("postedFile", getFile);
  data.append("renamefile", rename);
  data.append('pathUpload', `\\Register\\${path}\\`);

  const config = {
    method: "post",
    maxBodyLength: Infinity,
    url: `${import.meta.env.VITE_APP_TRR_API_URL}/UploadFile/Upload_File`,
    headers: {
      'Authorization': `Bearer ${import.meta.env.MODE === 'localhost' || import.meta.env.MODE === 'dev' ? "" : await getValidToken()}`,
    },
    data: data,
  };
  try {
    const reponse = await axios.request(config);
    if (reponse.status == 200) {
      return reponse.data;
    }
  } catch (e) {
    console.log(e);
  }
}

export async function plg_uploadFileUser(
  element: any,
  path: string
) {
  const data = new FormData();
  const getFile = element;

  data.append("postedFile", getFile);
  data.append("renamefile", getFile?.name.split(".")[0]);
  data.append('pathUpload', `\\User\\${path}\\`);

  const config = {
    method: "post",
    maxBodyLength: Infinity,
    url: `${import.meta.env.VITE_APP_TRR_API_URL}/UploadFile/Upload_File`,
    headers: {
      'Authorization': `Bearer ${import.meta.env.MODE === 'localhost' || import.meta.env.MODE === 'dev' ? "" : await getValidToken()}`
    },
    data: data,
  };
  try {
    const reponse = await axios.request(config);
    if (reponse.status == 200) {
      return reponse.data;
    }
  } catch (e) {
    console.log(e);
  }
}

export async function plg_uploadFileCampaign(
  element: any,
) {
  const data = new FormData();
  const getFile = element;

  data.append("postedFile", getFile);
  data.append("renamefile", getFile?.name.split(".")[0]);
  data.append('pathUpload', `\\Campaign_IMG\\`);

  const config = {
    method: "post",
    maxBodyLength: Infinity,
    url: `${import.meta.env.VITE_APP_TRR_API_URL}/UploadFile/Upload_File`,
    headers: {
      'Authorization': `Bearer ${import.meta.env.MODE === 'localhost' || import.meta.env.MODE === 'dev' ? "" : await getValidToken()}`
    },
    data: data,
  };
  try {
    const reponse = await axios.request(config);
    if (reponse.status == 200) {
      return reponse.data;
    }
  } catch (e) {
    console.log(e);
  }
}


export const Repayment_Salary_Deduct_Import_Preview = async (element: any, currentUsers: any) => {
  const data = new FormData();
  const getFile = element;

  data.append('CurrentAccessModelJson', currentUsers);
  data.append('file', getFile);

  const config = {
    method: 'post',
    maxBodyLength: Infinity,
    url: `${import.meta.env.VITE_APP_TRR_API_URL}/RepaymentSalaryDeduct/Repayment_Salary_Deduct_Import_Preview`,
    headers: {
      'Authorization': `Bearer ${import.meta.env.MODE === 'localhost' || import.meta.env.MODE === 'dev' ? "" : await getValidToken()}`
    },
    data: data
  };

  try {
    const reponse = await axios.request(config);
    if (reponse.status == 200) {
      return reponse.data;
    }
  } catch (e) {
    console.log(e);
  }

}
export const Repayment_Salary_Deduct_Import = async (element: any, currentUsers: any, sendData: any) => {
  const data = new FormData();
  const getFile = element;

  data.append('CurrentAccessModelJson', currentUsers);
  data.append('RepaymentSalaryDeductModelListJson', sendData);
  data.append('file', getFile);

  const config = {
    method: 'post',
    maxBodyLength: Infinity,
    url: `${import.meta.env.VITE_APP_TRR_API_URL}/RepaymentSalaryDeduct/Repayment_Salary_Deduct_Import`,
    headers: {
      'Authorization': `Bearer ${import.meta.env.MODE === 'localhost' || import.meta.env.MODE === 'dev' ? "" : await getValidToken()}`
    },
    data: data
  };

  try {
    const reponse = await axios.request(config);
    if (reponse.status == 200) {
      return reponse.data;
    }
  } catch (e) {
    console.log(e);
  }

}

