import React from "react";
import { listWaiveRepayment } from "../../../../../libs/columeCustomTable";
import TextFieldTextarea from "../../../../components/MUI/TextFieldTextarea";
import { _POST } from "../../../../service/mas";
import CustomTableSelect from "../../../../components/datatable/CustomTableSelect";
import { dateFormatTimeTH, numberWithCommas } from "../../../../../libs/dataControl";
import TextFieldAmountMoney from "../../../../components/MUI/TextFieldAmountMoney";
import DatePickerBasic from "../../../../components/MUI/DatePickerBasic";
import ButtonAll from "../../../../components/ButtonAll";
import { useDispatch } from "react-redux";
import { endEndLoadScreen, startLoadScreen } from "../../../../../redux/actions/loadingScreenAction";
import { useListWaive } from "../core/ListWaiveProvider";

interface WaiveRepayment {
    dataAccount?: any;
    dataSelect?: any;
    setDataSelect?: (data: any) => void
    note?: string
    setNote?: (val: string) => void
}

export default function WaiveRepayment({ dataAccount, dataSelect, setDataSelect, note, setNote }: WaiveRepayment) {
    const { valueWaive, setValueWaive, waiveDate, setWaiveDate } = useListWaive();
    const [dataList, setDataList] = React.useState<any[]>([]);
    const dispatch = useDispatch()

    const onChangeWaive = (val: string, index: number, data: any) => {
        // Update the charge amount in the data list
        setDataList((prev: any[]) => {
            const updated = [...prev];
            if (updated[index]) {
                updated[index] = {
                    ...updated[index],
                    waive_charge_amount: parseFloat(val) || 0,
                };
            }
            return updated;
        });

        // Update the waive value state
        setValueWaive((prev: any[]) => {
            const updated = [...prev];
            if (updated[index]) {
                updated[index] = {
                    ...updated[index],
                    waive_charge_amount: parseFloat(val) || 0,
                    id: data.id
                };
            } else {
                updated[index] = {
                    waive_charge_amount: parseFloat(val) || 0,
                    id: data.id
                };
            }
            // console.log('Updated waive values:', updated);
            return updated;
        });
    };

    const getChargeByAccountId = async () => {
        const datasend = {
            "account_id": dataAccount?.id,
            "date": null
        }
        try {
            const response = await _POST(datasend, "Waive/Waive_Repayment_Get")
            if (response && response.status == "success") {
                const { data: result } = response
                const newdata: any = []

                // Initialize waive values array
                const initialWaiveValues: any = []

                Array.isArray(result) && result.forEach((el, index) => {
                    // Determine if this item can be waived
                    const canWaive = el.is_waive && el.charge_amount > 0;

                    // Set select field for checkbox
                    el.select = canWaive ? "select" : "";

                    // Format display fields
                    el.due_date_ = dateFormatTimeTH(el?.due_date, "DD/MM/YYYY");
                    el.charge_amount_ = el.charge_amount ? numberWithCommas(el.charge_amount.toFixed(2)) : "0.00";
                    el.waive_charge_amount = el.charge_amount;
                    el.charge_balance_ = el.charge_balance ? numberWithCommas(el.charge_balance.toFixed(2)) : "0.00";

                    // Create TextFieldAmountMoney for editable charge amount
                    if (canWaive) {
                        el.charge_amount_waive = (
                            <TextFieldAmountMoney
                                value={el.charge_amount}
                                limitNumber={el.charge_amount}
                                onchange={(val: any) => onChangeWaive(val, index, el)}
                                disabled
                            />
                        );

                        // Initialize waive values
                        initialWaiveValues[index] = {
                            charge_amount: el.charge_amount,
                            waive_charge_amount: el.charge_amount,
                            id: el.id,
                            original_amount: el.charge_amount // Keep track of original amount
                        };
                    } else {
                        el.charge_amount_waive = "";
                    }

                    // Set waive flag
                    el.is_waive = canWaive;

                    newdata.push(el)
                })

                // Set initial waive values
                setValueWaive(initialWaiveValues);
                setDataList(newdata);

            }

        } catch (e) {
            console.log(e);

        }
    }

    React.useEffect(() => {
        if (dataAccount) {
            getChargeByAccountId()
        }
    }, [dataAccount])

    const totalSum = dataSelect.reduce(
        (sum: number, row: any) => sum + parseFloat(row[`${`charge_amount`}`]),
        0
    );


    return (
        <div>
            <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 mx-5 gap-x-4">
                <DatePickerBasic labelname="วันที่ Waive" valueStart={waiveDate} onchangeStart={setWaiveDate} />
                {/* <DatePickerBasic labelname="คำนวนดอกเบี้ยล่าสุดถึงวันที่" onchangeStart={(el: any) => { }}/>
        <div>
          <div className="flex justify-end space-x-2">
            <ButtonAll btnName="คำนวน Charge" handleOnClick={() => { }} />
          </div>
        </div> */}
            </div>
            <CustomTableSelect
                // titlename="รายการใบเสร็จที่ทำยกเว้นได้"
                colum={listWaiveRepayment}
                rows={dataList}
                noDataname="ไม่พบรายการใบเสร็จที่ทำยกเว้นได้"
                columnDisabled="is_waive"
                setDataSelect={setDataSelect}
            />
            <div className="grit grid-cols-1 sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-1 px-10">
                <div className="flex justify-end space-x-2">
                    <label htmlFor="" className="pt-2">สรุปยอดรวมที่ยกเว้น</label>
                    <TextFieldAmountMoney value={totalSum} disabled />
                </div>
            </div>
            <div className="grit grid-cols-1 sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-1 px-10">
                <TextFieldTextarea labelname="หมายเหตุ" value={note} onchange={setNote} />
            </div>
        </div>
    );
}
