import React from "react";
import ButtonAll from "../../../../components/ButtonAll";
import CustomTable from "../../../../components/datatable/CustomTable";
import CustomizedDialogs from "../../../../components/MUI/CustomizedDialogs";
import ReceiptList from "./WaiveCharge";
import { dateFormatTime, dateFormatTimeTH, numberWithCommas, setCurrentUsers } from "../../../../../libs/dataControl";
import { setValueMasView } from "../../../../../libs/setGetCallBackVaule";
import { useDispatch, useSelector } from "react-redux";
import DetailReceiptList from "./DetailReceiptList";
import { useAuth } from "../../../modules/auth";
import { SnackbarSet } from "../../../../components/MUI/SnackbarSet";
import { _POST } from "../../../../service/mas";
import { confirmModal } from "../../../../components/MUI/Comfirmmodal";
import { endEndLoadScreen, startLoadScreen } from "../../../../../redux/actions/loadingScreenAction";
import { useListWaive } from "../core/ListWaiveProvider";
import { historyWaive } from "../../../../../libs/columeCustomTable";
import { Massengmodal } from "../../../../components/MUI/Massengmodal";
import { titleName } from "..";
import WaiveRepayment from "./WaiveRepayment";

interface ListWaive {
  account?: any;
}

export default function ListWaive({ account }: ListWaive) {
  const menuFunc = useSelector((state: any) => state?.menuFunc);
  const screen_name = useSelector((state: any) => state?.labelName);
  const lovTitle = useSelector((state: any) => state?.lov_title?.lov_title);
  const { currentUser } = useAuth();
  const dispatch = useDispatch()
  const { itemView, optionWaiveType, actionName, waiveNote, waiveDate, setWaiveDate, setWaiveNote, valueWaive, setValueWaive } = useListWaive();
  const storeproduc = useSelector((state: any) => state?.produc?.produc);
  const [waiveByAccount, setWaiveByAccount] = React.useState<any>([]);
  const [isOpenWaive, setIsOpenWaive] = React.useState<any>(false);
  const [dataSelect, setDataSelect] = React.useState([]);
  const [isOpenDetail, setIsOpenDetail] = React.useState(null);


  const customer = React.useMemo(() => {
    if (itemView) {
      return itemView[0]?.CustomerModel[0];
    }
  }, [itemView]);

  const Waive_Get_By_Account_Id = async () => {
    const datasend = {
      "WaiveModel": {
        "id": null,
        "acct_id": account?.id,
        "waive_no": null,
        "waive_type": actionName == "WaiveCharge" ? "waive_type_charge" : actionName == "WaiveRepayment" ? "waive_type_repayment" : null
      }
    }
    try {
      const response = await _POST(datasend, "Waive/Waive_Get")
      if (response && response.status == "success") {
        const { data: result } = response
        const newdata: any = []
        Array.isArray(result) && result.forEach((el) => {
          el.waive_date_ = el?.waive_date ? dateFormatTimeTH(el?.waive_date, "DD/MM/YYYY") : "";
          el.waive_amount_ = el?.waive_amount ? numberWithCommas(el?.waive_amount.toFixed(2)) : "";
          el.tran_date_ = el?.tran_date ? dateFormatTimeTH(el?.tran_date, 'DD/MM/YYYY') : "";
          el.waive_type_name = el?.waive_type ? `${setValueMasView(optionWaiveType, el.waive_type, "id", "lov1")} (${setValueMasView(optionWaiveType, el.waive_type, "id", "lov2")})` : "";
          el.detail = (
            <ButtonAll
              btnName="ดูรายละเอียด"
              size="btn-sm"
              handleOnClick={() => setIsOpenDetail(el)}
            />
          );
          newdata.push(el)
        })
        setWaiveByAccount(newdata);
      }

    } catch (e) {
      console.log(e);

    }
  }


  const createWaive = async () => {
    if (dataSelect.length == 0) {
      SnackbarSet("กรุณาเลือกรายการที่ต้องการยกเว้น", "error", 4000)
      return
    }
    if (!waiveDate) {
      SnackbarSet("กรุณาเลือกวันที่ Waive", "error", 4000)
      return
    }
    const checkWaive = valueWaive.filter((item: any) => item?.waive_charge_amount == 0);
    if (checkWaive.length > 0) {
      SnackbarSet("กรุณากรอกจำนวนเงินที่ต้องการยกเว้น", "error", 4000)
      return
    }
    confirmModal.createModal("ต้องการยกเว้นใช่หรือไม่", "info", async () => {
      // dispatch(startLoadScreen());
      const newDataSelect: any[] = []
      Array.isArray(dataSelect) && dataSelect.forEach((el: any) => {
        const waiveValue = valueWaive.find((item: any) => item?.id == el.id);
        if (waiveValue) {
          const setObj = {
            id: el.id,
            waive_amount: waiveValue.waive_charge_amount
          }
          newDataSelect.push(setObj)
        }
      })
      const dataSend = {
        "WaiveModel": {
          "waive_date": dateFormatTime(waiveDate, "YYYY-MM-DD"),
          "acct_id": account?.id,
          "waive_type": actionName == "WaiveCharge" ? "waive_type_charge" : actionName == "WaiveRepayment" ? "waive_type_repayment" : null,
          "note": waiveNote === "" ? waiveNote : null,
        },
        "ChargesModelList": newDataSelect,
        "CurrentAccessModel": await setCurrentUsers(currentUser, screen_name, "Create_Waive")
      }

      try {
        const response = await _POST(dataSend, "Waive/Create_Waive")
        if (response && response.status == "success") {
          dispatch(endEndLoadScreen());
          Massengmodal.createModal("ยกเว้นเรียบร้อยแล้ว", "success", () => {
            setIsOpenWaive(false)
            Waive_Get_By_Account_Id()
            setDataSelect([])
            setWaiveDate(null)
            setWaiveNote("")
          })
        } else {
          dispatch(endEndLoadScreen());
          SnackbarSet(response, "error", 4000)
        }
      } catch (e) {
        console.log(e);
        dispatch(endEndLoadScreen());
        SnackbarSet(String(e), "error", 4000)
      }
    })
  }

  React.useEffect(() => {
    if (account) {
      Waive_Get_By_Account_Id()
    }
  }, [account])

  React.useEffect(() => {
    console.log(actionName);

  }, []);

  return (
    <div>
      <CustomTable
        titlename="ประวัติการยกเว้น"
        colum={historyWaive}
        // colum={[]}
        rows={waiveByAccount}
        noDataname="ไม่พบประวัติการยกเว้น"
        btnAdd={
          <ButtonAll
            btnName={`${menuFunc?.Waive} ${titleName[`${actionName}`]}`}
            className={`btn btn-success ${menuFunc?.Waive ? "" : "d-none"}`}
            handleOnClick={() =>
              setIsOpenWaive(true)
            }
          />
        }
      />
      <CustomizedDialogs
        titleName={`บันทึก ${titleName[`${actionName}`]}`}
        isOpen={isOpenWaive}
        handleClose={() => {
          setIsOpenWaive(false);
          setDataSelect([])
          setWaiveDate(null)
          setWaiveNote("")
        }}
        handleSave={() => { createWaive() }}
        maxWidth="xl"
        elementBody={
          actionName == "WaiveCharge" ? <ReceiptList dataAccount={account} dataSelect={dataSelect} setDataSelect={setDataSelect} />
            : actionName == "WaiveRepayment" ? <WaiveRepayment dataAccount={account} dataSelect={dataSelect} setDataSelect={setDataSelect} /> : null}
      />
      {/* <CustomizedDialogs
        titleName="เลือกรายการเรียกเก็บที่ต้องการยกเว้น"
        isOpen={isOpenWaive.notpaid}
        handleClose={() => {
          setIsOpenWaive({ ...isOpenWaive, ...{ notpaid: false } });
        }}
        maxWidth="xl"
        elementBody={<SelectCharge />}
      /> */}
      <CustomizedDialogs
        titleName="การยกเว้น"
        isOpen={isOpenDetail != null}
        handleClose={() => { setIsOpenDetail(null) }}
        maxWidth="lg"
        elementBody={<DetailReceiptList dataWaive={isOpenDetail} />}
        backdrop
      />
    </div>
  );
}
