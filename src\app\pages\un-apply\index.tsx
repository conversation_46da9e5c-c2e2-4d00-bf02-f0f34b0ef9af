import React from 'react'
import CustomerSearch from './component/search/CustomerSearch'
import { ListUnApplyProvider, useListUnApply } from './core/ListUnApplyProvider'
import { useColumnResponse } from '../../../components/datatable/core/ColumnProvider';
import { useDispatch, useSelector } from 'react-redux';
import { formatReturn, formatThaiCitizenId, formatThaiPhone } from '../../../../libs/dataControl';
import { _POST } from '../../../service/mas';
import { setValueMasView } from '../../../../libs/setGetCallBackVaule';
import { UserActionsCellCustomer } from '../../../components/datatable/UserActionsCellCustomer';
import { SnackbarSet } from '../../../components/MUI/SnackbarSet';
import { endEndLoadScreen, startLoadScreen } from '../../../../redux/actions/loadingScreenAction';
import { getDistrict, getProvince, getSubDistrict } from '../../../service/mas/address';
import { KTCard } from '../../../_metronic/helpers';
import DataTable from '../../../components/datatable';
import CustomerAccount from './component/ViewCustomerAccount/CustomerAccount';
import { UnApplyModal } from './component/modal/UnapplyModal';
import { getLovAll } from '../../../service/lov';

function UnApply() {
    const lovTitle = useSelector((state: any) => state?.lov_title?.lov_title);
    const lovSex = useSelector((state: any) => state?.lov_sex?.lov_sex);
    const { columnCustomer } = useColumnResponse();
    const {
        actionName,
        setActionName,
        itemView,
        setItemView,
        customerNunber,
        fullName,
        citizenId,
        sex,
        email,
        phone,
        setOptionProvinces,
        setOptionDistricts,
        setOptionSubDistricts,
        setOptionUnApplyType,
        setOptionUnApplyStatus,
    } = useListUnApply();
    const dispatch = useDispatch();
    const [dataList, setDataList] = React.useState<any>([]);

    const customerGet = async () => {
        const datasend = {
            CustomerModel: {
                id: null,
                user_id: null,
                cus_no: customerNunber ? customerNunber : null,
                search_name: fullName ? fullName : null,
                citizen_id: citizenId ? formatReturn(citizenId) : "",
                sex: sex ? sex.lov1 : null,
                email: email ? email : null,
                phone: phone ? formatReturn(phone) : null,
            },
        };
        try {
            const response = await _POST(datasend, "Customer/Customer_Get");
            if (response && response.status == "success") {
                const data = response.data;
                const newData: any = [];
                Array.isArray(data) &&
                    data.forEach(async (el) => {
                        el.fullName = `${setValueMasView(lovTitle, el.title, "id", 'lov1')} ${el.fname} ${el.lname}`;
                        el.citizen_id_ = formatThaiCitizenId(el.citizen_id);
                        el.phone_ = formatThaiPhone(el.phone);
                        el.sex_ = `${setValueMasView(lovSex, el.sex, "id", 'lov1')}`;
                        el.action = (
                            <UserActionsCellCustomer width="200"
                                handleOnClick={(name) => {
                                    setActionName(name);
                                    customerGetWithId(el);
                                }}
                            />
                        );
                        newData.push(el);
                    });
                await setDataList(newData);
            } else if (response && response.status == "error") {
                SnackbarSet(response.error_message, "error");
            } else {
                console.log(response);
            }
        } catch (e) {
            console.log(e);
        }
    };


    const customerGetWithId = async (el: any) => {
        dispatch(startLoadScreen());
        const datasend = {
            cus_id: el.id,
        };
        try {
            const response = await _POST(
                datasend,
                "Customer/Customer_Get_With_Id"
            );
            if (response && response.status == "success") {
                dispatch(endEndLoadScreen());
                setItemView(response.data);
            } else if (response && response.status == "error") {
                dispatch(endEndLoadScreen());
                SnackbarSet(response.error_message, "error");
            } else {
                dispatch(endEndLoadScreen());
                console.log(response);
                SnackbarSet(response, "error");
                SnackbarSet(`Call API ${response}`, "error");
            }
        } catch (e) {
            dispatch(endEndLoadScreen());
            console.log(e);
            SnackbarSet(`${e}`, "error");
        }
    };

    const _getMasAddress = async () => {
        const responses = await Promise.all([
            getProvince(),
            getDistrict(0),
            getSubDistrict(0),
            // other requests
            getLovAll({ lov_type: "UpApplyType" }),
            getLovAll({ lov_type: "UpApplyStatus" }),
        ]);
        if (responses && responses.length > 0) {
            const [provinces, districts, subDistricts, unApplyType, unApplyStatus] = responses;
            setOptionProvinces(provinces);
            setOptionDistricts(districts);
            setOptionSubDistricts(subDistricts);
            setOptionUnApplyType(unApplyType);
            setOptionUnApplyStatus(unApplyStatus);
        }
    }

    React.useEffect(() => {
        customerGet();
        _getMasAddress()
    }, []);
    return (
        <div>
            <CustomerSearch handleOnSearch={customerGet} />
            <KTCard>
                <DataTable
                    rows={dataList}
                    headCells={columnCustomer}
                    headercolor="from-gray-200 bg-gradient-to-b"
                />
            </KTCard>
            {itemView && (
                <UnApplyModal titleName={`${titleName[`${actionName}`]} / รายการที่ยังไม่ดำเนินการ (Un Apply)`}>
                    <CustomerAccount />
                </UnApplyModal>
            )}
        </div>
    )
}

export default function UnApplyWrapper() {
    return (
        <div>
            <ListUnApplyProvider>
                <UnApply />
            </ListUnApplyProvider>
        </div>
    )
}

const titleName: Record<string, string> = {
    SmallBalanceWriteOff: "Small Balance Write Off",
    UnApplyList: "Un Apply List",
};
