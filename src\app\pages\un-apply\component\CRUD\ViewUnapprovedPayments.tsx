import React from 'react'
import { dateFormatTimeTH } from '../../../../../../libs/dataControl'
import { setValueMas } from '../../../../../../libs/setGetCallBackVaule'
import { useSelector } from 'react-redux'
import TextFieldSizes from '../../../../../components/MUI/TextFieldSizes'
import TextFieldAmountMoney from '../../../../../components/MUI/TextFieldAmountMoney'

interface ViewUnapprovedPayments {
    customerModel?: any
    accountModel?: any
    dataView?: any
}

export default function ViewUnapprovedPayments({ customerModel, accountModel, dataView }: ViewUnapprovedPayments) {
    const storeproduc = useSelector((state: any) => state?.produc?.produc);
    const [productName, setProductName] = React.useState<string>("");
    const setDataProduct = async () => {
        if (accountModel) {
            const produc = await setValueMas(
                storeproduc,
                accountModel.product_id,
                "id"
            );
            setProductName(produc?.product_name);
        }
    };

    React.useEffect(() => {
        setDataProduct();
    }, [accountModel]);
    console.log(dataView);
    return (
        <div>
            <div className='grid grid-cols-1 sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-1 mx-5'>
                <div className='flex space-x-5'>
                    <p className={"text-xl"}>หมายเลขลูกค้า : {customerModel?.cus_no}</p>  <p className={"text-xl"}>หมายเลขบัญชี : {accountModel?.acct_no}</p>  <p className={"text-xl"}>ชื่อ-นามสกุล : {`${customerModel?.fname} ${customerModel?.lname}`}</p>
                </div>
                <div className='flex space-x-5'>
                    <p className={"text-xl"}>วันทีสร้างบัญชี : {accountModel?.acct_date ? dateFormatTimeTH(accountModel?.acct_date, "DD/MM/YYYY") : ""}</p>  <p className={"text-xl"}>ประเภทผลิตภัณฑ์ : {productName}</p>
                </div>
                {/* <div className='flex space-x-5'>
                    <p className={"text-xl"}>วันที่รับชำระ : {dataView?.un_apply_date_}</p>  <p className={"text-xl"}>ยอดชำระที่ยังไม่อนุมัติ : {dataView?.un_apply_amount_}</p>  <p className={"text-xl"}>ยอดชำระที่ยังไม่อนุมัติ คงเหลือ : {dataView?.un_apply_balance_}</p>
                </div> */}
            </div>
            <div className='grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 mx-5 gap-x-5'>
                <TextFieldSizes labelname='ประเภท' value={dataView?.un_apply_type_} disabled/>
                <TextFieldSizes labelname='สถานะ' value={dataView?.un_apply_status_} disabled/>
                <div></div>
                <TextFieldSizes labelname='วันที่เกิด Un Apply' value={dataView?.un_apply_date_} disabled/>
                <TextFieldAmountMoney labelname='ยอด Un Apply' value={dataView?.un_apply_amount} disabled/>
                <TextFieldAmountMoney labelname='ยอด Un Apply คงเหลือ' value={dataView?.un_apply_balance} disabled/>

            </div>
        </div>
    )
}
