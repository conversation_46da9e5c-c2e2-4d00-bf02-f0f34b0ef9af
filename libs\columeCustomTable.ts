export const paymentHistory = [
  {
    id: "repmt_date_",
    numeric: "center",
    disablePadding: false,
    label: "วันที่ชำระ",
    select: false,
    colWidth: 150,
  },
  {
    id: "repmt_amount_",
    numeric: "center",
    disablePadding: false,
    label: "ยอดที่ชำระ",
    select: false,
    colWidth: 150,
  },
  {
    id: "repmt_by",
    numeric: "center",
    disablePadding: false,
    label: "คนที่ชำระ",
    select: false,
    colWidth: 200,
  },
  {
    id: "posting_date_",
    numeric: "center",
    disablePadding: false,
    label: "วันที่เกิดรายการ",
    select: false,
    colWidth: 200,
  },
  {
    id: "detail",
    numeric: "center",
    disablePadding: false,
    label: "รายละเอียด",
    select: false,
    colWidth: 200,
  },
];

export const paymentDetail = [
  {
    id: "rownum",
    numeric: "center",
    disablePadding: false,
    label: "ลำดับ",
    select: false,
    colWidth: 100,
  },
  {
    id: "pmtc_name",
    numeric: "left",
    disablePadding: false,
    label: "รายการ",
    select: false,
    colWidth: 250,
  },
  {
    id: "repmt_amount_",
    numeric: "right",
    disablePadding: false,
    label: "ยอดที่ชำระ",
    select: false,
    colWidth: 150,
  },
];

export const billDetail = [
  {
    id: "rownum",
    numeric: "center",
    disablePadding: false,
    label: "ลำดับ",
    select: false,
    colWidth: 150,
  },
  {
    id: "list_detail",
    numeric: "left",
    disablePadding: false,
    label: "รายการ",
    select: false,
    colWidth: 150,
  },
  {
    id: "list_amount_",
    numeric: "right",
    disablePadding: false,
    label: "ยอดที่ชำระ",
    select: false,
    colWidth: 150,
  },
];
export const historyPaymentDetail = [
  {
    id: "rownum",
    numeric: "center",
    disablePadding: false,
    label: "ลำดับ",
    select: false,
    colWidth: 150,
  },
  {
    id: "list_",
    numeric: "left",
    disablePadding: false,
    label: "รายการ",
    select: false,
    colWidth: 150,
  },
  {
    id: "totol_paid",
    numeric: "right",
    disablePadding: false,
    label: "ยอดที่ชำระ",
    select: false,
    colWidth: 150,
  },
];
export const historyWaive = [
  {
    id: "waive_no",
    numeric: "center",
    disablePadding: false,
    label: "หมายเลขการยกเว้น",
    select: false,
    colWidth: 150,
  },
  {
    id: "waive_date_",
    numeric: "center",
    disablePadding: false,
    label: "วันที่ยกเว้น",
    select: false,
    colWidth: 150,
  },
  {
    id: "tran_date_",
    numeric: "center",
    disablePadding: false,
    label: "วันที่ทำรายการ",
    select: false,
    colWidth: 150,
  },
  {
    id: "waive_amount_",
    numeric: "right",
    disablePadding: false,
    label: "จำนวนเงินรวมที่ยกเว้น",
    select: false,
    colWidth: 150,
  },
  {
    id: "detail",
    numeric: "center",
    disablePadding: false,
    label: "ดูรายการ",
    select: false,
    colWidth: 150,
  },
];

export const listReceipt = [
  {
    id: "select",
    numeric: "center",
    disablePadding: false,
    label: "เลือก",
    select: true,
    colWidth: 150,
  },
  {
    id: "charge_des",
    numeric: "left",
    disablePadding: false,
    label: "รายการ",
    select: false,
    colWidth: 150,
  },
  {
    id: "due_date_",
    numeric: "right",
    disablePadding: false,
    label: "วันที่ครบกำหนดชำระ",
    select: false,
    colWidth: 150,
  },
  {
    id: "charge_amount_",
    numeric: "right",
    disablePadding: false,
    label: "ยอดเรียกเก็บ",
    select: false,
    colWidth: 150,
  },
  {
    id: "charge_balance_",
    numeric: "right",
    disablePadding: false,
    label: "ยอดคงเหลือ",
    select: false,
    colWidth: 150,
  },
  {
    id: "charge_amount_waive",
    numeric: "center",
    disablePadding: false,
    label: "จำนวนเงินที่เวฟ",
    select: false,
    colWidth: 150,
  },
];
export const columnSmallBalanceWriteOff = [
  {
    id: "select",
    numeric: "center",
    disablePadding: false,
    label: "เลือก",
    select: true,
    colWidth: 150,
  },
  {
    id: "charge_des",
    numeric: "left",
    disablePadding: false,
    label: "รายการ",
    select: false,
    colWidth: 150,
  },
  {
    id: "due_date_",
    numeric: "right",
    disablePadding: false,
    label: "วันที่ครบกำหนดชำระ",
    select: false,
    colWidth: 150,
  },
  {
    id: "charge_amount_",
    numeric: "right",
    disablePadding: false,
    label: "ยอดเรียกเก็บ",
    select: false,
    colWidth: 150,
  },
  {
    id: "charge_balance_",
    numeric: "right",
    disablePadding: false,
    label: "ยอดคงเหลือ",
    select: false,
    colWidth: 150,
  }
];

export const detailRrceipt = [
  {
    id: "charge_description",
    numeric: "left",
    disablePadding: false,
    label: "รายการ",
    select: false,
    colWidth: 150,
  },
  {
    id: "charge_due_date_",
    numeric: "center",
    disablePadding: false,
    label: "วันที่ครบกำหนดชำระ",
    select: false,
    colWidth: 150,
  },
  {
    id: "waive_amount_",
    numeric: "right",
    disablePadding: false,
    label: "จำนวนเงินที่เวฟ",
    select: false,
    colWidth: 150,
  },
];

export const selectCharge = [
  {
    id: "",
    numeric: "center",
    disablePadding: false,
    label: "เลือก",
    select: false,
    colWidth: 150,
  },
  {
    id: "",
    numeric: "left",
    disablePadding: false,
    label: "รายการ",
    select: false,
    colWidth: 150,
  },
  {
    id: "",
    numeric: "center",
    disablePadding: false,
    label: "วันที่ครบกำหนดชำระ",
    select: false,
    colWidth: 150,
  },
  {
    id: "",
    numeric: "right",
    disablePadding: false,
    label: "ยอดเรียกเก็บ",
    select: false,
    colWidth: 150,
  },
  {
    id: "",
    numeric: "right",
    disablePadding: false,
    label: "ยอดคงเหลือ",
    select: false,
    colWidth: 150,
  },
  {
    id: "",
    numeric: "right",
    disablePadding: false,
    label: "จำนวนเงินที่เวฟ",
    select: false,
    colWidth: 150,
  },
];
export const paymentSuspensionInformation = [
  {
    id: "hold_s_date_",
    numeric: "center",
    disablePadding: false,
    label: "วันที่เริ่มต้นระงับ",
    select: false,
    colWidth: 150,
  },
  {
    id: "hold_e_date_",
    numeric: "center",
    disablePadding: false,
    label: "วันที่สิ้นสุดระงับ",
    select: false,
    colWidth: 150,
  },
  {
    id: "note",
    numeric: "left",
    disablePadding: false,
    label: "หมายเหตุ",
    select: false,
    colWidth: 300,
  },
  {
    id: "action",
    numeric: "center",
    disablePadding: false,
    label: "จัดการ",
    select: false,
    colWidth: 150,
  }
];
export const unapprovedPayments = [
  {
    id: "account_no",
    numeric: "center",
    disablePadding: false,
    label: "หมายเลขบัญชี",
    select: false,
    colWidth: 150,
  },
  {
    id: "un_apply_no",
    numeric: "center",
    disablePadding: false,
    label: "Tran No",
    select: false,
    colWidth: 150,
  },
  {
    id: "un_apply_type_",
    numeric: "center",
    disablePadding: false,
    label: "ประเภท",
    select: false,
    colWidth: 150,
  },
  {
    id: "un_apply_date_",
    numeric: "center",
    disablePadding: false,
    label: "วันที่",
    select: false,
    colWidth: 150,
  },
  {
    id: "un_apply_amount_",
    numeric: "right",
    disablePadding: false,
    label: "ยอดชำระที่ยังไม่อนุมัติ",
    select: false,
    colWidth: 150,
  },
  {
    id: "un_apply_balance_",
    numeric: "right",
    disablePadding: false,
    label: "ยอดชำระที่ยังไม่อนุมัติ คงเหลือ",
    select: false,
    colWidth: 250,
  },
  {
    id: "un_apply_status_",
    numeric: "center",
    disablePadding: false,
    label: "สถานะ",
    select: false,
    colWidth: 250,
  },
  {
    id: "action",
    numeric: "center",
    disablePadding: false,
    label: "จัดการ",
    select: false,
    colWidth: 150,
  },
];
export const paymentsWithoutAccount = [
  {
    id: "repmt_date_",
    numeric: "center",
    disablePadding: false,
    label: "วันที่บันทึกการชำระแบบไม่มีบัญชี",
    select: false,
    colWidth: 250,
  },
  {
    id: "repmt_amount_",
    numeric: "right",
    disablePadding: false,
    label: "จำนวนเงิน",
    select: false,
    colWidth: 150,
  },
  {
    id: "bank_id_origin_neme",
    numeric: "center",
    disablePadding: false,
    label: "ธนาคารต้นทาง",
    select: false,
    colWidth: 150,
  },
  {
    id: "acct_no_origin",
    numeric: "center",
    disablePadding: false,
    label: "เลขที่บัญชีธนาคารต้นทาง",
    select: false,
    colWidth: 150,
  },
  {
    id: "acct_name_origin",
    numeric: "center",
    disablePadding: false,
    label: "ชื่อหน้าบัญชีต้นทาง",
    select: false,
    colWidth: 150,
  },
  {
    id: "bank_id_dest_name",
    numeric: "center",
    disablePadding: false,
    label: "ธนาคารปลายทาง",
    select: false,
    colWidth: 150,
  },
  {
    id: "acct_no_dest",
    numeric: "center",
    disablePadding: false,
    label: "เลขที่บัญชีปลายทาง",
    select: false,
    colWidth: 150,
  },
  {
    id: "acct_name_dest",
    numeric: "center",
    disablePadding: false,
    label: "ชื่อหน้าบัญชีปลายทาง",
    select: false,
    colWidth: 150,
  },
  {
    id: "no_book_status",
    numeric: "center",
    disablePadding: false,
    label: "สถานะ",
    select: false,
    colWidth: 150,
  },
  {
    id: "action",
    numeric: "center",
    disablePadding: false,
    label: "จัดการ",
    select: false,
    colWidth: 150,
  },
];
export const loanClosingInformation = [
  {
    id: "",
    numeric: "center",
    disablePadding: false,
    label: "หมายเลขสัญญา",
    select: false,
    colWidth: 200,
  },
  {
    id: "",
    numeric: "center",
    disablePadding: false,
    label: "แคปเปญ Code",
    select: false,
    colWidth: 150,
  },
  {
    id: "",
    numeric: "center",
    disablePadding: false,
    label: "วันที่สัญญา",
    select: false,
    colWidth: 150,
  },
  {
    id: "",
    numeric: "center",
    disablePadding: false,
    label: "อัตตราดอกเบี้ย (%)",
    select: false,
    colWidth: 150,
  },
  {
    id: "",
    numeric: "center",
    disablePadding: false,
    label: "จำนวนงวด",
    select: false,
    colWidth: 150,
  },
  {
    id: "",
    numeric: "center",
    disablePadding: false,
    label: "ยอดสินเชื่อ",
    select: false,
    colWidth: 150,
  },
  {
    id: "",
    numeric: "center",
    disablePadding: false,
    label: "ยอดสินเชื่อคงเหลือ",
    select: false,
    colWidth: 150,
  },
  {
    id: "",
    numeric: "center",
    disablePadding: false,
    label: "ดอกเบี้ย",
    select: false,
    colWidth: 150,
  },
  {
    id: "",
    numeric: "center",
    disablePadding: false,
    label: "จัดการ",
    select: false,
    colWidth: 150,
  },
];


export const previewCharges = [
  {
    id: "select",
    numeric: "center",
    disablePadding: false,
    label: "เลือก",
    select: true,
    colWidth: 50,
  },
  {
    id: "plan_no",
    numeric: "center",
    disablePadding: false,
    label: "หมายเลขสัญญา",
    select: false,
    colWidth: 100,
  },
  {
    id: "charge_des",
    numeric: "left",
    disablePadding: false,
    label: "รายการ",
    select: false,
    colWidth: 150,
  },
  {
    id: "due_date_",
    numeric: "right",
    disablePadding: false,
    label: "วันที่ครบกำหนดชำระ",
    select: false,
    colWidth: 150,
  },
  {
    id: "charge_amount_",
    numeric: "right",
    disablePadding: false,
    label: "ยอดเรียกเก็บ",
    select: false,
    colWidth: 150,
  },
  {
    id: "charge_balance_",
    numeric: "right",
    disablePadding: false,
    label: "ยอดคงเหลือ",
    select: false,
    colWidth: 150,
  },
  {
    id: "charge_amount_waive",
    numeric: "center",
    disablePadding: false,
    label: "จำนวนเงินที่เวฟ",
    select: false,
    colWidth: 150,
  },
];
export const previewRepaymentSalaryDeductionImport = [
  {
    id: "acct_no",
    numeric: "center",
    disablePadding: false,
    label: "เลขที่บัญชี",
    select: true,
    colWidth: 150,
  },
  {
    id: "cus_no",
    numeric: "center",
    disablePadding: false,
    label: "หมายเลขลูกค้า",
    select: false,
    colWidth: 150,
  },
  {
    id: "fullname",
    numeric: "left",
    disablePadding: false,
    label: "ชื่อลูกค้า",
    select: false,
    colWidth: 150,
  },
  {
    id: "repmt_salade_amount_",
    numeric: "right",
    disablePadding: false,
    label: "ยอดรับชำระ",
    select: false,
    colWidth: 150,
  }
];
export const previewRepaymentSalaryDeductionView = [
  {
    id: "rownum",
    numeric: "center",
    disablePadding: false,
    label: "ลำดับ",
    select: true,
    colWidth: 100,
  },
  {
    id: "acct_no",
    numeric: "center",
    disablePadding: false,
    label: "เลขที่บัญชี",
    select: true,
    colWidth: 150,
  },
  {
    id: "cus_no",
    numeric: "center",
    disablePadding: false,
    label: "หมายเลขลูกค้า",
    select: false,
    colWidth: 150,
  },
  {
    id: "fullname",
    numeric: "left",
    disablePadding: false,
    label: "ชื่อลูกค้า",
    select: false,
    colWidth: 150,
  },
  {
    id: "repmt_salade_amount_",
    numeric: "right",
    disablePadding: false,
    label: "ยอดรับชำระ",
    select: false,
    colWidth: 150,
  }
];