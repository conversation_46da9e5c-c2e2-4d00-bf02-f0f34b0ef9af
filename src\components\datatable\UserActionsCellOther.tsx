import { FC, useEffect } from "react";
import { MenuComponent } from "../../_metronic/assets/ts/components";
import { Tooltip } from "@mui/material";
import { useSelector } from "react-redux";
import { checkMenuPermisctionListOther } from "../../../libs/constants";
import { Menu } from "../../../types/user";

type Props = {
  handleOnClick?: (name: string) => void;
  hiddenStatusRow?: string;
  menuOtherName: "NOTE" | "NOBOOK" | "HOLD" | "UNAPPLY" | "ASSIGN";
  viewName: string;
  meneWidth?: "150px" | "200px" | "250px" | "300px";
  funcMenuShow?: string[];
};

const UserActionsCellOther: FC<Props> = ({ handleOnClick, hiddenStatusRow, menuOtherName, viewName, meneWidth = "150px", funcMenuShow }) => {
  const menuFuncAll = useSelector((state: any) => state?.menuFuncAll);
  const menuFuncList = checkMenuPermisctionListOther(menuFuncAll, menuOtherName);
  menuFuncList?.sort((a: Menu, b: Menu) => String(a.menu_func_sequence).localeCompare(String(b.menu_func_sequence)));

  useEffect(() => {
    MenuComponent.reinitialization();
  }, []);

  if (hiddenStatusRow === "View") {
    return (
      <>
        {/* <Tooltip title={"จัดการ"}> */}
        <a
          href="#"
          className="btn btn-light-warning  btn-active-light-warning btn-sm"
          data-kt-menu-trigger="click"
          data-kt-menu-placement="bottom-end"
        >
          {`...`}

          {/* <KTIcon iconName="down" className="fs-5 m-0" /> */}
        </a>
        {/* </Tooltip> */}
        {/* begin::Menu */}
        <div
          className={`menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-bold fs-7 w-${meneWidth} py-4`}
          data-kt-menu="true"
        >
          <div className="menu-item px-3">
            <a
              className="menu-link px-3"
              onClick={() => handleOnClick && handleOnClick(viewName)}
            >
              {'ดูข้อมูล'}
            </a>
          </div>

          {/* end::Menu item */}
        </div>
        {/* end::Menu */}
      </>
    );
  }

  return (
    <>
      {/* <Tooltip title={"จัดการ"}> */}
      <a
        // href="#"
        className="btn btn-light-warning  btn-active-light-warning btn-sm"
        data-kt-menu-trigger="click"
        data-kt-menu-placement="bottom-end"
      >
        {`...`}

        {/* <KTIcon iconName="down" className="fs-5 m-0" /> */}
      </a>
      {/* </Tooltip> */}
      {/* begin::Menu */}
      <div
        className={`menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-bold fs-7 w-${meneWidth} py-4`}
        data-kt-menu="true"
      >
        {menuFuncList?.map((item: Menu, index: number) =>
        (
          funcMenuShow?.includes(item.func_name) ?
            null :
            <div key={index} className="menu-item px-3">
              <a className="menu-link px-3" onClick={() => handleOnClick && handleOnClick(item.func_name)}>
                {item.display_name}
              </a>
            </div>
        ))}
        {menuFuncList?.length == 0 &&
          <div className="menu-item px-3">
            <a
              className="menu-link px-3"
              onClick={() => handleOnClick && handleOnClick(viewName)}
            >
              {'ดูข้อมูล'}
            </a>
          </div>
        }
        {/* end::Menu item */}
      </div>
      {/* end::Menu */}
    </>
  );
};

export { UserActionsCellOther };
