import React from "react";
import TextFieldSizes from "../../../../components/MUI/TextFieldSizes";
import DatePickerBasic from "../../../../components/MUI/DatePickerBasic";
import { DataDis } from "../../../../../types/disburse";
import dayjs from "dayjs";
import { _formatNumber } from "../../../../../libs/dataControl";
import { useAuth } from "../../../modules/auth";
import { getLovAll } from "../../../../service/lov";
import { Holiday } from "./DopaymentLoans";

interface SummaryTransactions {
  dataSelect: DataDis[];
  setDayPayment: (date: dayjs.Dayjs | undefined) => void;
  dayPayment: dayjs.Dayjs | undefined;
  dataHoliday: Holiday[];
}

export default function SummaryTransactions({
  dataSelect,
  setDayPayment,
  dayPayment,
  dataHoliday
}: SummaryTransactions) {
  const { currentUser } = useAuth();
  const [transactionDate, setTransactionDate] = React.useState<dayjs.Dayjs | null>(dayjs(new Date()));
  const [minDate, setMinDate] = React.useState<dayjs.Dayjs | null>(null);

  const summaryPayments = React.useMemo(() => {
    let sum = 0;
    Array.isArray(dataSelect) &&
      dataSelect.forEach((el, index) => {
        sum += el.approve_fin_amount;
      });
    const newObj: any = new Object();
    newObj.count = dataSelect?.length;
    newObj.sum = _formatNumber(sum);
    return newObj;
  }, [dataSelect]);

  function adjustToNextValidDay(today: dayjs.Dayjs): dayjs.Dayjs {
    let currentDay = today.startOf("day");

    // Extract holiday dates from dataHoliday
    const holidays = dataHoliday.map((holiday) =>
      dayjs(holiday.holiday_date).format("YYYY-MM-DD")
    );

    // Helper function to check if a date is a holiday or weekend
    const isHolidayOrWeekend = (date: dayjs.Dayjs): boolean => {
      const day = date.day(); // 0 = Sunday, 6 = Saturday
      const formattedDate = date.format("YYYY-MM-DD");

      // Check if it's a weekend
      if (day === 0 || day === 6) return true;

      // Check if it's a holiday
      return holidays.includes(formattedDate);
    };

    // Loop until we find a valid day
    while (isHolidayOrWeekend(currentDay)) {
      currentDay = currentDay.add(1, "day").startOf("day");
    }
    console.log(currentDay, 'currentDaycurrentDaycurrentDay');
    return currentDay;
  }

  // Disable weekends and holidays function
  const disableWeekendsAndHolidays = (date: Date): boolean => {
    // Check if the date is a weekend
    const day = dayjs(date).format("dddd");
    if (day === "เสาร์" || day === "อาทิตย์") {
      return true; // Disable the date if it's Saturday or Sunday
    }

    // Check if the date matches any holiday in dataHoliday
    const formattedDate = dayjs(date).format("YYYY-MM-DD");
    const isHoliday = dataHoliday.some(
      (holiday) =>
        dayjs(holiday.holiday_date).format("YYYY-MM-DD") === formattedDate
    );

    return isHoliday; // Disable the date if it's a holiday
  };

  
  React.useEffect(() => {
    const setDate = async () => {
      const disbursedDate: any = await getLovAll({ id: "advance_disburse_day", type_code: "Constant" });
      const day = dayjs().add(Number(disbursedDate?.[0]?.lov1), "day").startOf("day");
      const toDay = adjustToNextValidDay(day);
      console.log(toDay, 'toDaytoDaytoDay');
      
      setDayPayment(toDay);
      setMinDate(dayjs());
    }
    setDate();
  }, [dataHoliday]);

  return (
    <div>
      <div className="container">
        <div className="row">
          <div className="col-md-12">
            <TextFieldSizes
              labelname="จำนวนรายการที่ทำจ่าย"
              value={summaryPayments?.count}
              disabled
            />
          </div>
          <div className="col-md-12">
            <TextFieldSizes
              labelname="ยอดทำจ่ายทั้งหมด"
              value={summaryPayments?.sum}
              disabled
            />
          </div>
          <div className="col-md-12">
            <TextFieldSizes
              labelname="พนักงานที่ทำจ่าย"
              value={currentUser ? String(currentUser?.employee_username) : ""}
              disabled
            />
          </div>
          <div className="col-md-12 pb-2">
            <DatePickerBasic
              labelname="วันที่ทำรายการ"
              valueStart={transactionDate}
              onchangeStart={() => { }}
              disabled
            />
          </div>
          <div className="col-md-12">
            <DatePickerBasic
              labelname="วันที่จ่าย"
              valueStart={dayPayment}
              onchangeStart={setDayPayment}
              disableHighlightToday
              disableWeekends={disableWeekendsAndHolidays}
              minDate={minDate}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
