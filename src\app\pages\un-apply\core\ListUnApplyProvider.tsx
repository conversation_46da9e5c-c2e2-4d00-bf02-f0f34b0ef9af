import { createContext, FC, useContext, useState } from "react";
import { ID, WithChildren } from "../../../../_metronic/helpers";
import { Adds, initialListView, ListUnApplyContextProps, Texts } from "./models";
import dayjs from "dayjs";

const ListUnApplyContext = createContext<ListUnApplyContextProps>(initialListView);
const ListUnApplyProvider: FC<WithChildren> = ({ children }) => {
  const [actionName, setActionName] = useState<string>(initialListView.actionName);
  const [itemView, setItemView] = useState<Array<any[]>>(
    initialListView.itemView
  );

  const [customerNunber, setCustomerNunber] = useState<Texts>(
    initialListView.customerNunber
  );
  const [fullName, setFullName] = useState<Texts>(
    initialListView.fullName
  );
  const [citizenId, setCitizenId] = useState<Texts>(
    initialListView.citizenId
  );
  const [sex, setSex] = useState<any>(
    initialListView.sex
  );
  const [email, setEmail] = useState<Texts>(
    initialListView.email
  );
  const [phone, setPhone] = useState<Texts>(
    initialListView.phone
  );

   // MASTER Address
   const [optionProvinces, setOptionProvinces] = useState<Array<any[]>>(
    initialListView.optionProvinces
  );
  const [optionDistricts, setOptionDistricts] = useState<Array<any[]>>(
    initialListView.optionDistricts
  );
  const [optionSubDistricts, setOptionSubDistricts] = useState<Array<any[]>>(
    initialListView.optionSubDistricts
  );
  // LOV
  const [optionUnApplyType, setOptionUnApplyType] = useState<Array<any[]>>(
    initialListView.optionUnApplyType
  );
  const [optionUnApplyStatus, setOptionUnApplyStatus] = useState<Array<any[]>>(
    initialListView.optionUnApplyStatus
  );


  return (
    <ListUnApplyContext.Provider
      value={{
        actionName,
        setActionName,
        itemView,
        setItemView,

        customerNunber,
        fullName,
        citizenId,
        sex,
        email,
        phone,
        setCustomerNunber,
        setFullName,
        setCitizenId,
        setSex,
        setEmail,
        setPhone,
        // MASTER Address
        optionProvinces,
        setOptionProvinces,
        optionDistricts,
        setOptionDistricts,
        optionSubDistricts,
        setOptionSubDistricts,
        // LOV
        optionUnApplyType,
        setOptionUnApplyType,
        optionUnApplyStatus,
        setOptionUnApplyStatus,
      }}
    >
      {children}
    </ListUnApplyContext.Provider>
  );
};

const useListUnApply = () => useContext(ListUnApplyContext);

export { ListUnApplyProvider, useListUnApply };
