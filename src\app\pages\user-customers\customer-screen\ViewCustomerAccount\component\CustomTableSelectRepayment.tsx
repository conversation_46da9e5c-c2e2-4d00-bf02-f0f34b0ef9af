import React, { useState } from "react";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import { Card, Checkbox } from "@mui/material";
import { numberWithCommas } from "../../../../../../../libs/dataControl";

interface CustomTableSelect {
  colum: any;
  rows?: any;
  titlename?: string;
  noDataname?: string;
  colSum?: string;
  btnAdd1?: React.ReactNode;
  btnAdd?: React.ReactNode;
  componentColSum?: React.ReactNode;
  columnDisabled?: string
  isCheck?: any
  isSingleSelect?: boolean
  setDataSelect?: (data: any) => void
}

export default function CustomTableSelectRepayment({
  colum,
  rows,
  titlename,
  noDataname = "ไม่พบรายการ",
  colSum = "",
  btnAdd1,
  btnAdd,
  componentColSum,
  columnDisabled = "",
  isCheck,
  isSingleSelect,
  setDataSelect
}: CustomTableSelect) {
  const totalSum = rows.reduce(
    (sum: number, row: any) => sum + parseFloat(row[`${colSum}`]),
    0
  );

  // const [isCheck, setIsCheck] = useState<any>([]);

  const handleSelectAll = (e: any) => {
    if (e.target.checked) {
      const newSelected = columnDisabled == "" ? rows.map((el: any) => el) : rows.filter((el: any) => el[`${columnDisabled}`] == true);
      // setIsCheck(newSelected);
      setDataSelect && setDataSelect(newSelected);
      return;
    }
    // setIsCheck([]);
    setDataSelect && setDataSelect([]);
  };

  const handleClick = (selectedItem: any) => {
    if (!isSingleSelect) {
      const newData = isCheck;
      for (const i in newData) {
        if (Object.is(selectedItem, newData[i])) {
          newData.splice(i, 1);
          setDataSelect && setDataSelect([]);
          setDataSelect && setDataSelect(newData);
          return;
        }
      }
      newData.push(selectedItem);
      setDataSelect && setDataSelect([]);
      setDataSelect && setDataSelect(newData);
      return;
    }


    if (!setDataSelect) return;

    // Helper function to get item ID
    const getItemId = (item: any) => {
      return item.id || item.acct_id || item.repayment_id || item.un_apply_id;
    };

    const selectedId = getItemId(selectedItem);
    const currentSelected = isCheck[0]; // Get currently selected item (if any)

    // Check if the clicked item is already selected
    const isCurrentlySelected = currentSelected && (
      selectedId ? getItemId(currentSelected) === selectedId
        : JSON.stringify(currentSelected) === JSON.stringify(selectedItem)
    );

    if (isCurrentlySelected) {
      // If clicking the same item, deselect it (empty array)
      setDataSelect([]);
    } else {
      // Select only this item (replace any previous selection)
      setDataSelect([selectedItem]);
    }
  };

  const _isSelected = (item: any) => {
    if (!isSingleSelect) {
      const newData = isCheck;
      for (const i in newData) {
        if (Object.is(el, newData[i])) {
          // console.log(true, i);
          return true;
        }
      }
      return false;
    }

    // // For single selection, check if this item is the selected one
    // const currentSelected = isCheck[0];

    // if (!currentSelected) return false;

    // // Helper function to get item ID (same as in handleClick)
    // const getItemId = (obj: any) => {
    //   return obj.id || obj.acct_id || obj.repayment_id || obj.un_apply_id;
    // };

    // const itemId = getItemId(item);
    // const selectedId = getItemId(currentSelected);

    // if (itemId && selectedId) {
    //   // Use ID-based comparison
    //   return itemId === selectedId;
    // } else {
    //   // Fallback to deep comparison
    //   return JSON.stringify(item) === JSON.stringify(currentSelected);
    // }
  };

  const rowCount = React.useMemo(() => {
    if (columnDisabled == "") {
      return rows.length
    } else {
      const row = rows.filter((el: any) => el[`${columnDisabled}`] == true)
      return row.length
    }
  }, [rows])

  return (
    <div className="bg-white border overflow-hidden sm:rounded-md my-5 mx-5">
      <div className="flex  px-6 pt-3 justify-between items-center">
        <label htmlFor="" className="fs-5 text-blue-500">
          {titlename}
        </label>
        {/* Add button */}
        <div className="flex items-center space-x-2">
          {btnAdd1 && btnAdd1}
          {btnAdd && btnAdd}
        </div>
      </div>
      <div className="p-5">
        <TableContainer className="border rounded-lg ">
          <Table sx={{ minWidth: 650 }} aria-label="simple table">
            <TableHead>
              <TableRow>
                {colum.map((headCell: any, index: number) => (
                  <TableCell
                    key={index}
                    align={"center"}
                    padding={headCell.disablePadding ? "none" : "normal"}
                    sx={{ minWidth: headCell?.colWidth }}
                    className="border-r border-r-gray-500/10 bg-gray-500/5"
                  >
                    {headCell.select && (
                      <Checkbox
                        color="primary"
                        // For single selection, disable indeterminate and checked states
                        indeterminate={false}
                        checked={false}
                        disabled={true} // Disable "Select All" for single selection
                        onClick={(e: any) => handleSelectAll(e)}
                        inputProps={{
                          "aria-label": "select all disabled for single selection",
                        }}
                      />
                    )}
                    <label className="text-gray-600 fw-bold">
                      {headCell.label}
                    </label>
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {rows &&
                rows.map((row: any, index: number) => {
                  return (
                    <TableRow
                      className={`hover:bg-blue-50 ${row[`${columnDisabled}`] ? `` : `bg-gray-500/5`}`}
                      tabIndex={-1}
                      key={index}
                    >
                      {colum?.map((column: any, index: number) => {
                        const value = row[column.id];
                        return (
                          value == "select" ? (
                            <TableCell
                              key={index + 1}
                              padding="checkbox"
                              align={column.numeric}
                              className={`border-r border-r-gray-500/10`}
                            >
                              <Checkbox
                                color="primary"
                                checked={_isSelected(row)}
                                onClick={() => handleClick(row)}
                              />
                            </TableCell>
                          ) : (
                            <TableCell
                              key={index + 1}
                              align={column.numeric}
                              className={`border-r border-r-gray-500/10`}
                            >
                              <label className="text-gray-600 fs-6">
                                {value}
                              </label>
                            </TableCell>
                          )

                        );
                      })}
                    </TableRow>
                  );
                })}
              {componentColSum && componentColSum}
              {colSum != "" && (
                <TableRow>
                  <TableCell
                    colSpan={colum.length - 1}
                    align="center"
                    className="border-r border-r-gray-500/10"
                  >
                    <label className="fs-6 text-gray-600">{"รวม"}</label>
                  </TableCell>
                  <TableCell
                    align="right"
                    className="border-r border-r-gray-500/10"
                  >
                    <label className="fs-6 text-gray-600">
                      {numberWithCommas(totalSum.toFixed(2))}
                    </label>
                  </TableCell>
                </TableRow>
              )}
              {Array.isArray(rows) && rows.length == 0 && (
                <TableRow>
                  <TableCell
                    colSpan={colum.length}
                    sx={{ width: "100%" }}
                    align="center"
                  >
                    <label className="fs-5 text-gray-500">{noDataname}</label>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </div>
    </div>
  );
}
