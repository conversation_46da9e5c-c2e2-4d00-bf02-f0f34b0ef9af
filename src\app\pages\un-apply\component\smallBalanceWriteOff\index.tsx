import React from 'react'
import CustomTableSelect from '../../../../../components/datatable/CustomTableSelect'
import { columnSmallBalanceWriteOff } from '../../../../../../libs/columeCustomTable';

export default function SmallBalanceWriteOff() {
    const [dataSelect, setDataSelect] = React.useState<any>([]);

    return (
        <div>
            <CustomTableSelect
                colum={columnSmallBalanceWriteOff}
                rows={[]}
                setDataSelect={setDataSelect}
            />
        </div>
    )
}
