import React, { useState } from 'react'
import BrowseFile from '../../../../../components/BrowseFile'
import { KTCard } from '../../../../../_metronic/helpers';
import AutocompletesAll from '../../../../../components/MUI/AutocompletesAll';
import TextFieldNumber from '../../../../../components/MUI/TextFieldNumber';
import TextFieldAmountMoney from '../../../../../components/MUI/TextFieldAmountMoney';
import TextFieldSizes from '../../../../../components/MUI/TextFieldSizes';
import { previewRepaymentSalaryDeductionImport } from '../../../../../../libs/columeCustomTable';
import ButtonAll from '../../../../../components/ButtonAll';
import { useListPaymentView } from '../../core/ListPaymentViewProvider';
import { Repayment_Salary_Deduct_Import, Repayment_Salary_Deduct_Import_Preview } from '../../../../../service/upload';
import { numberWithCommas, setCurrentUsers } from '../../../../../../libs/dataControl';
import DatePickerBasic from '../../../../../components/MUI/DatePickerBasic';
import { useAuth } from '../../../../modules/auth';
import { useDispatch, useSelector } from 'react-redux';
import dayjs from 'dayjs';
import { endEndLoadScreen, startLoadScreen } from '../../../../../../redux/actions/loadingScreenAction';
import DataTable from '../../../../../components/datatable';
import { SnackbarSet } from '../../../../../components/MUI/SnackbarSet';
import { confirmModal } from '../../../../../components/MUI/Comfirmmodal';
import { Massengmodal } from '../../../../../components/MUI/Massengmodal';
import { makeDataSend } from '.';

interface RepaymentSalaryDeductionImport {
    Repayment_Salary_Deduct_Get: (data: any) => void
}

export default function RepaymentSalaryDeductionImport({ Repayment_Salary_Deduct_Get }: RepaymentSalaryDeductionImport) {
    const dispatch = useDispatch()
    const { currentUser } = useAuth();
    const screen_name = useSelector((state: any) => state?.labelName);
    const [fileImport, setFileImport] = useState<any>();
    const [fileImportDataAll, setFileImportDataAll] = useState<any>([]);
    const [fileImportReView, setFileImportReView] = useState<any>([]);
    const { setItemImportData, companyGroups } = useListPaymentView();
    const [fileName, setFileName] = useState<string | undefined>("");
    const [dateImport, setDateImport] = useState<dayjs.Dayjs | null>(null);
    const [company, setCompany] = useState<any>(null);
    // check validate
    const [validateUpload, setValidateUpload] = useState<boolean>(false);
    const [validateCompany, setValidateCompany] = useState<boolean>(false);
    const formRef = React.useRef<any>(null);

    React.useEffect(() => {
        console.log(fileImport);
        const readData = async () => {
            if (fileImport) {
                setValidateUpload(false);
                dispatch(startLoadScreen())
                const currentUsers = await setCurrentUsers(currentUser, screen_name, "Repayment_Salary_Deduct_Import_Preview");
                const response = await Repayment_Salary_Deduct_Import_Preview(fileImport[0], JSON.stringify(currentUsers, null, 4));
                console.log(response, 'dataViewdataViewdataView');
                if (response && response.status == "success") {
                    const { data: data } = response;
                    setFileImportDataAll(data);
                    if (data.length > 0) {
                        const RepaymentSalaryDeductDetailModelList = data[0].RepaymentSalaryDeductDetailModelList
                        const dataView = RepaymentSalaryDeductDetailModelList.map((el: any) => {
                            return {
                                ...el,
                                repmt_salade_amount_: numberWithCommas(el.repmt_salade_amount)
                            }
                        })
                        setFileImportReView(dataView);
                        setDateImport(dayjs());
                        dispatch(endEndLoadScreen())
                    } else {
                        dispatch(endEndLoadScreen())
                    }
                } else {
                    dispatch(endEndLoadScreen())
                }
            }
        }
        readData();
    }, [fileImport]);

    const handleOnClickSave = async () => {
        if (!fileImport) {
            setValidateUpload(true);
            SnackbarSet("กรุณาเลือกไฟล์นำเข้า!", "error")
            return
        }
        setValidateUpload(false);
        if (!company) {
            setValidateCompany(true);
            SnackbarSet("กรุณาเลือกกลุ่มบริษัท!", "error")
            return
        }
        const currentUsers = await setCurrentUsers(currentUser, screen_name, "Repayment_Salary_Deduct_Import");
        const sendData = [{
            "com_group_id": company ? company.id : null,
            "repmt_salade_import_filename": fileImport[0].name,
            "repmt_salade_total_record": fileImportDataAll.length > 0 ? fileImportDataAll[0]?.repmt_salade_total_record : 0,
            "repmt_salade_total_amount": fileImportDataAll.length > 0 ? fileImportDataAll[0]?.repmt_salade_total_amount : 0,
            "RepaymentSalaryDeductDetailModelList": fileImportReView
        }]
        confirmModal.createModal("ต้องการนำเข้าไฟล์ตัดเงินเดือนใช่หรือไม่", "info", async () => {
            dispatch(startLoadScreen());
            const response = await Repayment_Salary_Deduct_Import(fileImport[0], JSON.stringify(currentUsers, null, 4), JSON.stringify(sendData, null, 4));
            if (response && response.status == "success") {
                dispatch(endEndLoadScreen());
                Massengmodal.createModal("นำเข้าไฟล์เรียบร้อยแล้ว", "success", () => {
                    setFileImport(undefined);
                    setFileImportDataAll([]);
                    setFileImportReView([]);
                    setCompany(null);
                    setDateImport(null);
                    Repayment_Salary_Deduct_Get(makeDataSend);
                })
            } else {
                dispatch(endEndLoadScreen());
                Massengmodal.createModal("นำเข้าไฟล์ไม่สำเร็จ กรุณาติดต่อเจ้าหน้าที่", "error", () => { })
            }
        })
    }

    const handleOnclose = () => {
        setItemImportData(false);
    };

    React.useEffect(() => {
        const firstErrorField = formRef.current.querySelector(".Mui-error");
        if (firstErrorField) {
            firstErrorField.scrollIntoView({ behavior: "smooth", block: "center" });
        }
    }, [validateCompany]);

    return (
        <div ref={formRef}>
            <div className='grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 pb-4'>
                <BrowseFile labelname='อัพโหลด File รับชำระแบบตัดเงินเดือน' selectedFile={fileName} setFile={setFileImport} setSelectedFile={setFileName} accept='.xlsx' ischeck={validateUpload} />
                <div></div>
                <div className='flex justify-end items-center space-x-2'>
                    <ButtonAll btnName='ดาวนโหลดแบบฟอร์ม' className='btn btn-primary' handleOnClick={() => { }} />
                </div>
            </div>
            <div>
                <KTCard>
                    <div className='text-2xl font-bold py-2 px-2'>สรุปรายการ</div>
                    <div className='grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 px-3 gap-4'>
                        <AutocompletesAll labelname='กลุ่มบริษัท' options={companyGroups} required='required' columnName='com_group_name' value={company} onchange={setCompany} validate={validateCompany} />
                        <TextFieldNumber labelname='จำนวนรายการที่นำเข้า' value={fileImportDataAll.length > 0 ? fileImportDataAll[0]?.repmt_salade_total_record : ""} disabled />
                        <TextFieldAmountMoney labelname='จำนวนเงินรวมที่รับชำระ' value={fileImportDataAll.length > 0 ? fileImportDataAll[0]?.repmt_salade_total_amount : ""} disabled />
                        <DatePickerBasic labelname='วันที่นำเข้าข้อมูล' valueStart={dateImport} onchangeStart={setDateImport} />
                        <TextFieldSizes labelname='User ที่นำเข้าข้อมูล' value={`${currentUser?.employee_fname_en} ${currentUser?.employee_lname_en}`} disabled />
                    </div>
                    <div className='py-2 px-2'>
                        <DataTable tableName='รายการนำเข้า' rows={fileImportReView} headCells={previewRepaymentSalaryDeductionImport} />
                    </div>
                </KTCard>
                <footer className='flex justify-end space-x-2 pt-5'>
                    <ButtonAll btnName='บันทึก' className='btn btn-success' handleOnClick={handleOnClickSave} />
                    <ButtonAll btnName='ปิด' className='btn btn-danger' handleOnClick={handleOnclose} />
                </footer>
            </div>
        </div>
    )
}
