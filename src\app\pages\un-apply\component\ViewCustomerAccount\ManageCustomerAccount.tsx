import React from "react";
import { KTCard } from "../../../../../_metronic/helpers";
import AutocompletesAll from "../../../../../components/MUI/AutocompletesAll";
import { setValueMas, setValueMasView } from "../../../../../../libs/setGetCallBackVaule";
import { useDispatch, useSelector } from "react-redux";
import { _formatNumber, dateFormatTimeTH, numberWithCommas, setCurrentUsers, } from "../../../../../../libs/dataControl";
import { Box, Tab, Tabs } from "@mui/material";
import { _POST } from "../../../../../service/mas";
import { useAuth } from "../../../../modules/auth";
import { useListUnApply } from "../../core/ListUnApplyProvider";
import UnApplyCRUD from "../CRUD/indext";
import TabPanel from "@mui/lab/TabPanel";
import TabContext from "@mui/lab/TabContext";
import TabList from "@mui/lab/TabList";
import SmallBalanceWriteOff from "../smallBalanceWriteOff";

interface ManageCustomerAccount {
}
export default function ManageCustomerAccount({ }: ManageCustomerAccount) {
  const dispatch = useDispatch()
  const { itemView, actionName } = useListUnApply();
  const screen_name = useSelector((state: any) => state?.labelName);
  const { currentUser } = useAuth();
  const storeproduc = useSelector((state: any) => state?.produc?.produc);
  const lovTitle = useSelector((state: any) => state?.lov_title?.lov_title);
  const addressType = useSelector((state: any) => state?.address_type?.address_type);
  const [optionAccount, setOptionAccount] = React.useState([]);
  const [account, setAccount] = React.useState<any>(null);
  const [productName, setProductName] = React.useState<string>("");
  const [isEditSendAddress, setIsEditSendAddress] = React.useState(false);
  const [valueSendAddress, setValueSendAddress] = React.useState("");
  const [channelTab, setChannelTab] = React.useState('Current');

  const handleChange = (event: React.SyntheticEvent, newValue: string) => {
    setChannelTab(newValue);
  };

  React.useEffect(() => {
    const data = itemView?.[0]?.AccountModel;
    setOptionAccount(data);
    setAccount(data?.[0]);
    setValueSendAddress(data?.[0]?.send_doc_address_type);
  }, [itemView]);

  const customerModel = React.useMemo(() => {
    return itemView[0]?.CustomerModel[0];
  }, [itemView]);


  const setDataProduct = async () => {
    if (account) {
      const produc = await setValueMas(storeproduc, account.product_id, "id");
      setProductName(produc?.product_name);
    }
  };

  const addressSendDoc = (type: string) => {
    const addressCitizen = itemView[0]?.CustomerAddressRegistrationModel;
    const addressCurrent = itemView[0]?.CustomerAddressCurrentModel;
    const addressWork = itemView[0]?.CustomerAddressWorkModel;
    if (type == "address_registration") {
      const [address] = addressCitizen;
      return `${address.address} อ.${address.amphur_name_th} ต.${address.tambon_name_th} จ.${address.province_name_th} ไปรษณีย์ ${address.zip_code}`;
    }
    if (type == "address_current") {
      const [address] = addressCurrent;
      return `${address.address} อ.${address.amphur_name_th} ต.${address.tambon_name_th} จ.${address.province_name_th} ไปรษณีย์ ${address.zip_code}`;
    }
    if (type == "address_work") {
      const [address] = addressWork;
      return `${address.address} อ.${address.amphur_name_th} ต.${address.tambon_name_th} จ.${address.province_name_th} ไปรษณีย์ ${address.zip_code}`;
    }
    return ""
  };

  React.useEffect(() => {
    setDataProduct();
  }, [account]);
  console.log(valueSendAddress);


  return (
    <KTCard>
      <div className="">
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 my-4 mx-10">
          <AutocompletesAll
            value={account}
            labelname="เลือกบัญชี"
            options={optionAccount}
            columnName="acct_no"
            onchange={setAccount}
          />
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 my-4 mx-10">
          <div className="bg-gray-100 p-4">
            <p>
              หมายเลขบัญชี :{" "}
              <span className="">{account?.acct_no}</span>
            </p>
            <p>
              วันสร้างบัญชี :{" "}
              <span className="">
                {account?.acct_date
                  ? dateFormatTimeTH(account?.acct_date, "DD/MM/YYYY HH:mm:ss")
                  : ""}
              </span>
            </p>
            <p>
              วันที่ออกบิลล่าสุด :{" "}
              <span className="">
                {account?.last_billing_date
                  ? dateFormatTimeTH(account?.last_billing_date, "DD/MM/YYYY HH:mm:ss")
                  : ""}
              </span>
            </p>
            <p>
              ยอดเงินต้นคงเหลือ :{" "}
              <span className="">
                {account?.account_total_prin_out
                  ? numberWithCommas(account.account_total_prin_out.toFixed(2))
                  : "0.00"}
              </span>
            </p>
          </div>
          <div className="bg-gray-100 p-4">
            {/* <p>
              หมายเลขลูกค้า :{" "}
              <span className="">{customerModel?.cus_no}</span>
            </p> */}
            <p>
              ประเภทผลิตภัณฑ์ : <span className="">{productName}</span>
            </p>
            <p>
              ยอดเงินที่ต้องชำระในงวดล่าสุด :{" "}
              <span className="">
                {account?.account_last_billing_amount
                  ? numberWithCommas(account?.account_last_billing_amount.toFixed(2))
                  : "0.00"}
              </span>
            </p>
            <p>
              ยอดดอกเบี้ยทั้งหมด :{" "}
              <span className="">
                {account?.account_total_int_amount
                  ? numberWithCommas(account?.account_total_int_amount.toFixed(2))
                  : "0.00"}
              </span>
            </p>
          </div>
          <div className="bg-gray-100 p-4">
            <p>
              ชื่อ-นามสกุล :{" "}
              <span className="">{`${customerModel?.title ? setValueMasView(lovTitle, customerModel?.title, "id", "lov1") : ''} ${customerModel?.fname} ${customerModel?.lname}`}</span>
            </p>
            <p>
              วันที่ครบกำหนดชำระเงินล่าสุด :{" "}
              <span className="">
                {account?.billing_due_date
                  ? dateFormatTimeTH(account?.billing_due_date, "DD/MM/YYYY HH:mm:ss")
                  : ""}
              </span>
            </p>
            <p>
              ยอดรวมค่าธรรมเนียมต่างๆ :{" "}
              <span className="">
                {account?.fee_amount
                  ? numberWithCommas(account?.fee_amount.toFixed(2))
                  : "0.00"}
              </span>
            </p>
          </div>
        </div>
        <div>
          <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-1 gap-4 my-4 mx-10">
            <div className="bg-gray-100 p-4">
              <div className="flex space-x-10 items-center">
                <div>
                  <div className="text-xl text-gray-900/50">ที่อยู่จัดส่งเอกสาร</div>
                  <div className="text-xl">{addressSendDoc(account?.send_doc_address_type)}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="grid grid-cols-1 border rounded-lg my-4 mx-10">
          {actionName == "UnApplyList" &&
            <Box sx={{ width: '100%', typography: 'body1' }}>
              <TabContext value={channelTab}>
                <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                  <TabList
                    onChange={handleChange}
                    aria-label="lab API tabs example"
                    textColor="secondary"
                    indicatorColor="secondary"
                    sx={{
                      '& .MuiTab-root': {
                        fontSize: '1.2rem',
                        fontWeight: 'bold',
                        textTransform: 'none', // optional: keep text casing as-is
                      },
                    }}
                  >
                    <Tab label="Current" value="Current" />
                    <Tab label="History" value="History" />
                  </TabList>
                </Box>
                <TabPanel value="Current">
                  <UnApplyCRUD accountModel={account} isTab="Current" />
                </TabPanel>
                <TabPanel value="History">
                  <UnApplyCRUD accountModel={account} isTab="History" />
                </TabPanel>
              </TabContext>
            </Box>
          }
          {actionName == "SmallBalanceWriteOff" &&
            <SmallBalanceWriteOff />
          }
        </div>
      </div>

    </KTCard>
  );
}

