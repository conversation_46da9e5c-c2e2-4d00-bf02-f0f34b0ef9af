import dayjs from "dayjs";
import { ID } from "../../../../_metronic/helpers";
import { SetStateAction, Dispatch } from "react";

export type Adds = undefined | null | boolean;
export type Texts = undefined | null | string;

export type ListUnApplyContextProps = {
  actionName: string;
  setActionName: Dispatch<SetStateAction<string>>;
  itemView?: any;
  setItemView: Dispatch<SetStateAction<any>>;
  customerNunber?: Texts;
  fullName?: Texts;
  citizenId?: Texts;
  sex?: any;
  email?: Texts;
  phone?: Texts;
  setCustomerNunber: Dispatch<SetStateAction<Texts>>;
  setFullName: Dispatch<SetStateAction<Texts>>;
  setCitizenId: Dispatch<SetStateAction<Texts>>;
  setSex: Dispatch<SetStateAction<any>>;
  setEmail: Dispatch<SetStateAction<Texts>>;
  setPhone: Dispatch<SetStateAction<Texts>>;


  // MASTER Address
  optionProvinces: any;
  setOptionProvinces: Dispatch<SetStateAction<any>>;
  optionDistricts: any;
  setOptionDistricts: Dispatch<SetStateAction<any>>;
  optionSubDistricts: any;
  setOptionSubDistricts: Dispatch<SetStateAction<any>>;
  // LOV
  optionUnApplyType: any;
  setOptionUnApplyType: Dispatch<SetStateAction<any>>;
  optionUnApplyStatus: any;
  setOptionUnApplyStatus: Dispatch<SetStateAction<any>>;
};

export const initialListView: ListUnApplyContextProps = {
  actionName: "",
  setActionName: () => { },
  setItemView: () => { },
  setFullName: () => { },
  setCitizenId: () => { },
  setSex: () => { },
  setEmail: () => { },
  setPhone: () => { },
  setCustomerNunber: () => { },
  // MASTER Address
  optionProvinces: [],
  setOptionProvinces: () => { },
  optionDistricts: [],
  setOptionDistricts: () => { },
  optionSubDistricts: [],
  setOptionSubDistricts: () => { },
  // LOV
  optionUnApplyType: [],
  setOptionUnApplyType: () => { },
  optionUnApplyStatus: [],
  setOptionUnApplyStatus: () => { },
};
