import React from "react";
import { listReceipt } from "../../../../../libs/columeCustomTable";
import TextFieldTextarea from "../../../../components/MUI/TextFieldTextarea";
import { _POST } from "../../../../service/mas";
import CustomTableSelect from "../../../../components/datatable/CustomTableSelect";
import { dateFormatTime, dateFormatTimeTH, numberWithCommas, setCurrentUsers } from "../../../../../libs/dataControl";
import TextFieldAmountMoney from "../../../../components/MUI/TextFieldAmountMoney";
import DatePickerBasic from "../../../../components/MUI/DatePickerBasic";
import ButtonAll from "../../../../components/ButtonAll";
import { useListWaive } from "../core/ListWaiveProvider";
import dayjs from "dayjs";
import CustomizedDialogs from "../../../../components/MUI/CustomizedDialogs";
import { TextField } from "@mui/material";
import TextFieldSizes from "../../../../components/MUI/TextFieldSizes";
import { useSelector } from "react-redux";
import { useAuth } from "../../../modules/auth";
import { confirmModal } from "../../../../components/MUI/Comfirmmodal";
import { Massengmodal } from "../../../../components/MUI/Massengmodal";

interface ReceiptList {
  dataAccount?: any;
  dataSelect?: any;
  setDataSelect?: (data: any) => void
}

export default function ReceiptList({ dataAccount, dataSelect, setDataSelect }: ReceiptList) {
  const screen_name = useSelector((state: any) => state?.labelName);
  const { currentUser } = useAuth();
  const [dataList, setDataList] = React.useState([]);
  const { waiveDate, setWaiveDate, waiveNote, setWaiveNote, valueWaive, setValueWaive } = useListWaive();
  const [latestDate, setLatestDate] = React.useState<dayjs.Dayjs | null>(null);
  const [isOpenCalculate, setIsOpenCalculate] = React.useState<any>(false);
  const [calculateDate, setCalculateDate] = React.useState<dayjs.Dayjs | null>(null);


  const onChangeWaive = (val: string, index: number, data: any) => {
    // Update the repayment data state
    setValueWaive((prev: any[]) => {
      const updated = [...prev]; // Clone the existing array
      if (updated[index]) {
        updated[index] = { ...updated[index], un_apply_balance: val, id: data.id }; // Update the specific object
      } else {
        updated[index] = { un_apply_balance: val, id: data.id }; // Initialize if the object doesn't exist
      }
      console.log(updated); // Log the updated array
      return updated;
    });
  };

  const getChargeByAccountId = async () => {
    const datasend = {
      "account_id": dataAccount?.id,
      "date": null
    }
    try {
      const response = await _POST(datasend, "Waive/Get_Charge_By_Account_Id")
      if (response && response.status == "success") {
        const { data: result } = response
        const newdata: any = []
        Array.isArray(result) && result.forEach((el, index) => {
          el.select = el.is_waive ? el.charge_amount == 0 ? "" : "select" : ""
          el.due_date_ = dateFormatTimeTH(el?.due_date, "DD/MM/YYYY");
          el.charge_amount_ = el.charge_amount ? numberWithCommas(el.charge_amount.toFixed(2)) : "0.00";
          el.charge_balance_ = el.charge_balance ? numberWithCommas(el.charge_balance.toFixed(2)) : "0.00";
          el.charge_amount_waive = el.is_waive ? el.charge_amount == 0 ? "" : (<TextFieldAmountMoney value={el.charge_amount} onchange={(event: any) => onChangeWaive(event.target.value, index, el)} />) : ""
          // el.charge_amount_waive = el.is_waive ? el.charge_amount == 0 ? "" : numberWithCommas(el.charge_amount.toFixed(2)) : ""
          el.is_waive = el.is_waive ? el.charge_amount == 0 ? false : true : false
          newdata.push(el)
        })

        const latestDate = getLatestIntCalEDate(result);
        setLatestDate(latestDate);
        setDataList(newdata);

      }

    } catch (e) {
      console.log(e);

    }
  }

  const getLatestIntCalEDate = (items: any[]): dayjs.Dayjs | null => {
    const dates = items
      .map(item => item.int_cal_e_date)
      .filter((date): date is string => !!date)
      .map(date => dayjs(date));

    if (dates.length === 0) return null;

    const latest = dates.reduce((max, d) => d.isAfter(max) ? d : max);
    return latest;
  };

  const Waive_Charge_Calculate = async () => {
    if (!calculateDate) {
      return
    }
    confirmModal.createModal("ต้องการคำนวนใช่ไหม", "info", async () => {
      const datasend = {
        "acct_no": dataAccount?.acct_no,
        "calculate_waive_charge_date": dateFormatTime(calculateDate, "YYYY-MM-DD"),
        "CurrentAccessModel": await setCurrentUsers(currentUser, screen_name, "Waive_Charge_Calculate")
      }
      try {
        const response = await _POST(datasend, "Waive/Waive_Charge_Calculate")
        if (response && response.status == "success") {
          Massengmodal.createModal("คำนวนเรียบร้อยแล้ว", "success", () => {
            getChargeByAccountId()
            setIsOpenCalculate(false)
          })
        }
      } catch (e) {
        console.log(e);

      }
    })
  }

  React.useEffect(() => {
    if (dataAccount) {
      getChargeByAccountId()
    }
  }, [dataAccount])

  const totalSum = dataSelect.reduce(
    (sum: number, row: any) => sum + parseFloat(row[`${`charge_amount`}`]),
    0
  );


  return (
    <div>
      <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 mx-5 gap-x-4">
        <DatePickerBasic labelname="วันที่ Waive" valueStart={waiveDate} onchangeStart={setWaiveDate} />
        <DatePickerBasic labelname="คำนวนดอกเบี้ยล่าสุดถึงวันที่" valueStart={latestDate} onchangeStart={setLatestDate} disabled />
        <div>
          <div className="flex justify-end space-x-2">
            <ButtonAll btnName="คำนวน Charge" handleOnClick={() => { setIsOpenCalculate(true) }} />
          </div>
        </div>
      </div>
      <CustomTableSelect
        // titlename="รายการใบเสร็จที่ทำยกเว้นได้"
        colum={listReceipt}
        rows={dataList}
        noDataname="ไม่พบรายการใบเสร็จที่ทำยกเว้นได้"
        columnDisabled="is_waive"
        setDataSelect={setDataSelect}
      />
      <div className="grit grid-cols-1 sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-1 px-10">
        <div className="flex justify-end space-x-2">
          <label htmlFor="" className="pt-2">สรุปยอดรวมที่ยกเว้น</label>
          <TextFieldAmountMoney value={totalSum} disabled />
        </div>
      </div>
      <div className="grit grid-cols-1 sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-1 px-10">
        <TextFieldTextarea labelname="หมายเหตุ" value={waiveNote} onchange={setWaiveNote} />
      </div>
      <CustomizedDialogs
        titleName="คำนวน Charge"
        isOpen={isOpenCalculate}
        handleClose={() => { setIsOpenCalculate(false) }}
        handleCalculate={() => { Waive_Charge_Calculate() }}
        maxWidth="sm"
        backdrop
        elementBody={
          <>
            <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-1 px-10">
              <TextFieldSizes labelname="เลขที่บัญชี" value={dataAccount?.acct_no} disabled />
              <DatePickerBasic labelname="คำนวนดอกเบี้ยล่าสุดถึงวันที่" valueStart={latestDate} onchangeStart={setLatestDate} disabled />
              <div className="pt-3">
                <DatePickerBasic
                  labelname="ถึงวันที่"
                  valueStart={calculateDate}
                  onchangeStart={setCalculateDate}
                  minDate={latestDate ? dayjs(latestDate).add(1, 'day') : null}
                />
              </div>
            </div>

          </>
        }
      />
    </div>
  );
}
